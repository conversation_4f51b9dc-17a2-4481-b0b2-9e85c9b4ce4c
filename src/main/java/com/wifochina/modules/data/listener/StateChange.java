package com.wifochina.modules.data.listener;

import lombok.Data;
import lombok.ToString;

/**
 * StateChange
 *
 * @since 3/30/2022 4:34 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString
public class StateChange {

    /** 状态当前值 */
    private int value;

    /** 状态旧值 */
    private int lastValue;

    /** 所属设备 */
    private String deviceId;

    /** 所属设备名称 */
    private String deviceName;

    /** 装备名称 */
    private String equipName;

    /** 事件column值 */
    private String column;

    /** 设备类型code */
    private Integer typeCode;

    /** 项目id */
    private String projectId;

    /** 时间(毫秒) */
    private long time;

    /** 设备是否维护 */
    private Boolean maintain;
}
