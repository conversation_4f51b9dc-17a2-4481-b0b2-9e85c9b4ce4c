package com.wifochina.modules.data.influxdb;

import lombok.Data;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "influx")
public class InfluxConfig {
    String url;
    String token;
    String org;

    @Value("${influx.bucket.realtime}")
    String realtimeBucket;

    @Value("${influx.bucket.forever}")
    String foreverBucket;

    @Value("${influx.bucket.mean}")
    String meanBucket;

    @Value("${influx.bucket.demand}")
    String demandBucket;
}
