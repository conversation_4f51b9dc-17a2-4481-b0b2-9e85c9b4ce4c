package com.wifochina.common.util;

/**
 * @since 2024-04-09 9:43 AM
 * <AUTHOR>
 */
public class HashUtil {
    public static int elfHash(String str) {
        int hash = 0;
        long x;

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            hash = (hash << 4) + c;
            if ((x = (hash & 0xf0000000L)) != 0) {
                hash ^= (int) (x >> 24);
                hash &= (int) ~x;
            }
        }

        return hash & 0xffff;
    }
}
