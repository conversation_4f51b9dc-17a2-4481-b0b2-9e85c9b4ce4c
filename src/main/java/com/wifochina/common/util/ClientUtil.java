package com.wifochina.common.util;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;

/**
 * 客户端工具
 *
 * <AUTHOR>
 */
public class ClientUtil implements Serializable {

	private static final long serialVersionUID = 4734197461620267293L;

	private static final String UNKNOWN = "unknown";


	public static String getUserAgent(HttpServletRequest request) {
		return request.getHeader("User-Agent");
	}

	public static String getReferer(HttpServletRequest request) {
		return request.getHeader("Referer");
	}


	public static String getIp(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}

}