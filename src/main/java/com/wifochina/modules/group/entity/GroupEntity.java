package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分组
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_group")
@ApiModel(value = "GroupEntity对象", description = "分组")
public class GroupEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组标识uuid")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "分组名称")
    private String name;

    @ApiModelProperty(value = "外部控制器(false关闭)(true打开）")
    private Boolean externalController;

    @ApiModelProperty(value = "计算收益开关(false关闭)(true打开）")
    private Boolean calcEarningsController;

    @ApiModelProperty(value = "分组储能收益开关(false关闭)(true打开）")
    private Boolean groupEarningsController;

    @ApiModelProperty(value = "控制需量开关(false关闭)(true打开）")
    private Boolean demandController;

    @ApiModelProperty(value = "需量控制模式 1负载开环控制模式 2电网闭环控制模式 3混合控制模式")
    private Integer demandControllerModel;

    @ApiModelProperty(value = "PV(光伏)模式开关(false关闭)(true打开）")
    private Boolean photovoltaicController;

    @ApiModelProperty(value = "PV(光伏)收益开关开关(false关闭)(true打开）")
    private Boolean pvProfitController;

    @ApiModelProperty(value = "DCDC(光伏)收益开关开关(false关闭)(true打开）")
    private Boolean dcdcProfitController;

    @ApiModelProperty(value = "光伏发电模式 1全额上网 2自发自用余量上网")
    private Integer photovoltaicModel;

    @ApiModelProperty(value = "是否系统开关(true 是)(false否)")
    private Boolean whetherSystem;

    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "是否开启防逆流")
    private Boolean antiReflux;

    @ApiModelProperty(value = "是否开启soc界限")
    private Boolean enableStopSoc;

    @ApiModelProperty(value = "是否接入load")
    private Boolean enableLoad;

    @ApiModelProperty(value = "是否接入load电表")
    private Boolean enableLoadGrid;

    @ApiModelProperty(value = "是否展示天气数据:false 不展示,true展示")
    private Boolean enableWeather;

    @ApiModelProperty(value = "是否开启光伏预测:0 不预测, 1 预测")
    private Boolean pvPrediction;

    @ApiModelProperty(value = "是否开启f负载预测:0 不预测, 1 预测")
    private Boolean loadPrediction;

    @ApiModelProperty(value = "10000-20000之间,隔100的整数值")
    private Integer externalControllerOffset;

    @ApiModelProperty(value = "是否接入储能设备EMS")
    private Boolean enableEms;

    @ApiModelProperty(value = "是否接入并网电表")
    private Boolean enableElectricGrid;

    @ApiModelProperty(value = "是否显示电网电量,默认展示")
    private Boolean enableShowElectricQuantity;

    @ApiModelProperty(value = "pcs型号")
    private String pcsCode;

    @ApiModelProperty(value = "是否接入风电")
    private Boolean enableWindPowerGeneration;

    @ApiModelProperty(value = "风电发电模式 1全额上网 2自发自用余量上网")
    private Integer windPowerModel;

    @ApiModelProperty(value = "是否接入柴发")
    private Boolean enableWoodPowerGeneration;

    @ApiModelProperty(value = "是否接入充电桩")
    private Boolean enableChargingPilePower;

    @ApiModelProperty(value = "是否接入燃气发电机")
    private Boolean enableGasPowerGeneration;

    @ApiModelProperty(value = "外部控制模式切换：是否根据远端控制自动切换，false为手动")
    private Boolean directPowerAutoControl;

    @ApiModelProperty(value = "外部控制模式自动时，切换开关的104遥控点位")
    private Integer directPowerControlIec104EnableIoa;

    @ApiModelProperty(value = "外部控制模式下，104协议中，公共地址")
    private Integer directPowerControlIec104CommonAddr;

    @ApiModelProperty(value = "外部控制模式下，104协议中，有功功率摇调地址")
    private Integer directPowerControlIec104ActiveIoa;

    @ApiModelProperty(value = "外部控制模式下，104协议中，无功功率摇调地址")
    private Integer directPowerControlIec104ReactiveIoa;

    @ApiModelProperty(value = "modbus协议中，有功功率寄存器地址(两个寄存器)")
    private Integer directPowerControlModbusActiveAddr;

    @ApiModelProperty(value = "modbus协议中，无功功率寄存器地址(两个寄存器)")
    private Integer directPowerControlModbusReactiveAddr;

    @ApiModelProperty(value = "是否有消防")
    private Boolean hasFireFighting;

    @ApiModelProperty(value = "是否显示电池电量")
    private Boolean showBatteryElectricity;

    @ApiModelProperty(value = "数据展示方式:true代表收益电表,false代表ems; 默认false")
    private Boolean showType;

    @ApiModelProperty(value = "需量收益(false关闭)(true打开）")
    private Boolean demandIncome;

    @ApiModelProperty(value = "需量告警阈值(超出多少就告警)")
    private Double demandAlarmThreshold;

    @ApiModelProperty(value = "需量计算模式:1固定时段15分钟;2固定时段30分钟;3一分钟滑窗")
    private Integer demandCalcModel;

    @ApiModelProperty(value = "容量控制开关(false关闭)(true打开）")
    private Boolean capacityController;

    @ApiModelProperty(value = "可控设备运行策略")
    private String controllableStrategies;

    @ApiModelProperty(value = "是否接入余热发电")
    private Boolean enableWastePowerGeneration;
}
