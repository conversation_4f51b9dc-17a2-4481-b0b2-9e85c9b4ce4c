package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * t_ammeter
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_ammeter")
@ApiModel(value = "AmmeterEntity对象", description = "AmmeterEntity对象")
public class AmmeterEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电表标识符id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "电表名称")
    private String name;

    @ApiModelProperty(value = "电表ip")
    private String ip;

    @ApiModelProperty(value = "电表端口")
    private Integer port;

    @ApiModelProperty(value = "1 PV电表、 2并网点电表、3负载电表")
    private Integer type;

    @ApiModelProperty(value = "电表型号，只支持 EMSMeter,DTSD3125,CEM9000")
    private String vendor;

    @ApiModelProperty(value = "CEM9000的测控号，1或者2")
    private Integer meterNum;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "是否反击，默认false")
    private Boolean reverse;

    @ApiModelProperty(value = "电量输出初始值")
    private Double outputHistoryInit;

    @ApiModelProperty(value = "电量输入初始值")
    private Double inputHistoryInit;

    @ApiModelProperty(value = "是否测控电表，默认为false 不是测控电表")
    private Boolean controllable;

    @ApiModelProperty(value = "是否维护,默认不维护false")
    private Boolean maintain;

    @ApiModelProperty(value = "是否计算收益,默认不维护false")
    private Boolean income;

    @ApiModelProperty(value = "CEM9000是否使用低压")
    private Boolean useLowSide;

    @ApiModelProperty(value = "高压侧 CT比")
    private Double highCtRatio;

    @ApiModelProperty(value = "高压侧 PT比")
    private Double highPtRatio;

    @ApiModelProperty(value = "高压侧保护CT比")
    private Double highProtectCtRatio;

    @ApiModelProperty(value = "低压侧 CT比")
    private Double lowCtRatio;

    @ApiModelProperty(value = "低压侧 PT比")
    private Double lowPtRatio;

    @ApiModelProperty(value = "是否抄表,默认不维护false")
    private Boolean meterReading;

    @ApiModelProperty(value = "序号")
    @TableField(value = "`index`")
    private Integer index;

    @ApiModelProperty(value = "是否是直流电表")
    private Boolean dcMeter;
}
