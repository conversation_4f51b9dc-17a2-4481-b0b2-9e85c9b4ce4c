package com.wifochina.modules.group.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.mapper.GroupMapper;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.group.vo.GroupGoVo;
import com.wifochina.modules.group.vo.GroupVO;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分组 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@Service
public class GroupServiceImpl extends ServiceImpl<GroupMapper, GroupEntity>
        implements GroupService {

    @Override
    public IPage<GroupVO> queryGroupVO(IPage<GroupVO> groupPage, String name, String projectId) {
        return this.baseMapper.queryGroupVO(groupPage, name, projectId);
    }

    @Override
    public List<GroupGoVo> queryGroup(String projectId) {
        return this.baseMapper.queryGroup(projectId);
    }

    /**
     * 查询系统分组 根据projectId
     *
     * @param projectId : 项目id
     * @return : GroupEntity
     */
    @Override
    public GroupEntity systemGroupEntity(String projectId) {
        return this.getOne(
                Wrappers.lambdaQuery(GroupEntity.class)
                        .eq(GroupEntity::getProjectId, projectId)
                        .eq(GroupEntity::getWhetherSystem, true));
    }

    @Override
    public List<GroupEntity> queryGroupsNotSystem(String projectId) {
        return this.list(
                Wrappers.lambdaQuery(GroupEntity.class)
                        .eq(GroupEntity::getProjectId, projectId)
                        .eq(GroupEntity::getWhetherSystem, false));
    }

    /**
     * 查询非系统分组并且是绑定了收入设备或者表计的分组
     *
     * @param projectId : 项目id
     * @return : List<GroupEntity>
     */
    @Override
    public List<GroupEntity> queryGroupsNotSystemAndBindIncomeDeviceOrMeter(String projectId) {
        return this.baseMapper.queryGroupsNotSystemAndBindIncomeDeviceOrMeter(projectId);
    }
}
