package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 协调控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_controller")
@ApiModel(value = "ControllerEntity对象", description = "协调控制器")
public class ControllerEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "uuid")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "ip")
    private String ip;

    @ApiModelProperty(value = "端口")
    private Integer port;

    @ApiModelProperty(value = "序列号")
    private String sn;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private String projectId;

}
