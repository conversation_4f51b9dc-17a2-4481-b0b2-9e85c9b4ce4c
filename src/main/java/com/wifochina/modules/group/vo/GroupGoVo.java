package com.wifochina.modules.group.vo;

import com.wifochina.modules.group.entity.GroupEntity;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 分组
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GroupGoVo extends GroupEntity {

    @ApiModelProperty(value = "关联的电表id")
    private List<String> ammeters;

    @ApiModelProperty(value = "关联的设备id")
    private List<String> devices;

    @ApiModelProperty(value = "关联的可控设备id")
    private List<String> controllables;
}
