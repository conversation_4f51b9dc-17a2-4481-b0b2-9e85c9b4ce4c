package com.wifochina.common.util;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class StringUtil {
	public static boolean isEmpty(Object value, boolean trim) {
		if (value == null) {
			return true;
		}
		String text = value instanceof String ? (String) value : value.toString();
		return (trim ? text.trim() : text).isEmpty();
	}
	public static boolean notEmpty(Object value, boolean trim) {
		return !isEmpty(value, trim);
	}
	/** 不执行trim */
	public static boolean isEmpty(Object value) {
		return value == null || (value instanceof String ? (String) value : value.toString()).isEmpty();
	}
	/** 不执行trim */
	public static boolean notEmpty(Object value) {
		return !isEmpty(value);
	}

	public static String trim(String value) {
		return value == null ? null : value.trim();
	}
	/**
	 * @param emptyToNull trim后，如果是空串，将返回null
	 */
	public static String trim(String value, boolean emptyToNull) {
		if (value == null){
			 return null;
		}
		value = value.trim();
		return emptyToNull && value.isEmpty() ? null : value;
	}
	
    /**
     * 切分后自动执行trim
     * @see #split(String, char, boolean, Integer)
     */
   	public static List<String> split(String text, final char splitChar) {
   		return split(text, splitChar, true, text.length() / 16);
   	}
    /**
	 * 非正则表达式方式split，大量计算时可以节省cpu<br>
	 * 如果做缓存时，注意String的特性——共享一个char数组，潜在的jvm内存泄漏 
	 * @param text	原始文本
	 * @param splitChar 分割字符
	 * @param initialCapacity 初始化List容量大小
	 * @return 切分后字符串
	 */
	public static List<String> split(String text, final char splitChar, boolean trim, Integer initialCapacity) {
		List<String> result = (initialCapacity == null || initialCapacity < 10) ? new ArrayList<String>()
				: new ArrayList<String>(initialCapacity);
		int start = 0, len = text.length();
		for (int i = 0; i < len; i++) {
			if (splitChar == text.charAt(i)) {
				String tmp = start == i ? "" : text.substring(start, i);
				result.add(trim ? tmp.trim() : tmp);
				start = i + 1;
			}
		}
		if (start <= len) {
			String tmp = text.substring(start);
			result.add(trim ? tmp.trim() : tmp);
		}
		return result;
	}
	
	/** 耗时只有{@linkplain java.math.BigInteger#toString(int) new BigInteger(bytes).toString(16)}的40% */
	public static String toHexString(byte[] bytes) {
    	StringBuilder buf = new StringBuilder(bytes.length * 2);
    	for (int i = 0, len = bytes.length, value; i < len; i++) {
    		value = bytes[i] & 0xff;
    		if (value < 0x10) {
				buf.append('0');
			}
			buf.append(Integer.toHexString(value));
		}
        return buf.toString();
	}
	private static int toInt(char ch) {
		return ch - (ch > '9' ? 'a' - 10 : '0');
	}
	/** 耗时只有{@linkplain java.math.BigInteger#toByteArray() new BigInteger(hex, 16).toByteArray()}的15% */
	public static byte[] toBytesFromHex(String hex) {
		int len = hex.length();
		
		byte[] bytes = new byte[(len + 1)/2];
		int pos = 0, i = 0;
		if ((hex.length() & 1) == 1) {
			bytes[pos++] = (byte) toInt(hex.charAt(i++));
		}
		
		for (; i < len;) {
			bytes[pos++] = (byte) (toInt(hex.charAt(i++)) << 4 | toInt(hex.charAt(i++)));
		}
		return bytes;
	}
	
	/**
     * 将输入字符串经过MD5处理后返回
     * @param values 待处理字符串
     * @return MD5之后的结果
     */
    public static String md5(String... values) {
    	return md5(Arrays.asList(values));
    }
    /** 将输入字符串经过MD5处理后返回 */
    public static String md5(Collection<String> values) {
    	MessageDigest messageDigest = getMessageDigest("MD5");
    	for (String value : values) {
    		if (value != null) {
				messageDigest.update(value.getBytes(StandardCharsets.UTF_8));
			}
    	}
        return toHexString(messageDigest.digest());
    }
    /** @param algorithm 例如：MD5 */
    public static MessageDigest getMessageDigest(String algorithm) {
    	try {
			return MessageDigest.getInstance(algorithm.toUpperCase());
		} catch (NoSuchAlgorithmException e) {
			throw new IllegalStateException("MD5 provider not exist!");
    	}
    }

    public static byte[] doFinal(String algorithm, byte[] key, int opmode, byte[] input) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
    	algorithm = trim(algorithm);
    	if (algorithm == null) {
			throw new IllegalArgumentException("加密算法不能为空");
		}
    	algorithm = algorithm.toUpperCase();
		String aesAlogrithm = "AES";
		int keyLen = 16;
		boolean privateKeyCheck = aesAlogrithm.equals(algorithm) && (key == null || key.length != keyLen);
		if (privateKeyCheck){
			throw new IllegalArgumentException("AES私钥长度必须是16");
		}
    	
		Cipher cipher = Cipher.getInstance(algorithm);
		cipher.init(opmode, new SecretKeySpec(key, algorithm));
		return cipher.doFinal(input);
    }
    /**
     * AES加密
     * @param key 私钥key
     * @param text 待加密字符串
     * @return 加密后的16进制数字串
     */
	public static String encodeAes(byte[] key, String text) throws IllegalArgumentException {
		try {
			byte[] result = doFinal("AES", key, Cipher.ENCRYPT_MODE, text.getBytes(StandardCharsets.UTF_8));
			return toHexString(result);
		} catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException | BadPaddingException e) {
			throw new IllegalArgumentException(e);
		}
	}
	/**
     * AES解密
     * @param key 私钥key
     * @param hex 待解密16进制串
     */
	public static String decodeAes(byte[] key, String hex) {
		try {
			byte[] result = doFinal("AES", key, Cipher.DECRYPT_MODE, toBytesFromHex(hex));
			return new String(result, StandardCharsets.UTF_8);
		} catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException | BadPaddingException e) {
			throw new IllegalArgumentException(e);
		}
	}
	
	public static String encodeUrl(Object value) {
		if (value == null){ 
			return "";
		}
		try {
			return URLEncoder.encode(value.toString(), "UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new IllegalStateException(e);
		}
	}
	public static String decodeUrl(Object value) {
		if (value == null){
			 return null;
		}
		try {
			return URLDecoder.decode(value.toString(), "UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new IllegalStateException(e);
		}
	}
	
	public static Properties toProps(String text) {
		Properties props = new Properties();
		try (InputStream in = new ByteArrayInputStream(text.getBytes(StandardCharsets.UTF_8))) {
			props.load(in);
		} catch (IOException e) {
			throw new IllegalArgumentException(e);
		}
		return props;
	}

	/** 扩展text到指定长度的字节, 长度不足循环补充，长度超过的从头开始执行(相加) */
	public static byte[] extendToByte(String text, int len) {
		byte[] target = new byte[len];
		if (isEmpty(text)) {
			return target;
		}
		
		char[] chars = text.toCharArray();
		int srcLen = chars.length;
		
		if (len <= srcLen) {
			for (int srcPos = 0, targetPos = 0; srcPos < srcLen; srcPos++, targetPos++) {
				if (targetPos >= len) {
					targetPos = 0;
				}
				target[targetPos] += chars[srcPos];
			}
		} else {
			for (int targetPos = 0, srcPos = 0; targetPos < len; targetPos++, srcPos++) {
				if (srcPos >= srcLen) {
					srcPos = 0;
				}
				target[targetPos] += chars[srcPos];
			}
		}
		return target;
	}
	
	/** Returns val represented by the specified number of hex digits. */
    private static String digits(long val, int digits) {
        long hi = 1L << (digits * 4);
        return Long.toHexString(hi | (val & (hi - 1))).substring(1);
    }

    /** uuid方法，结果中没有-符号，耗时只有replaceAll的30% */
	public static String uuid() {
		UUID uuid = UUID.randomUUID();
		long mostSigBits = uuid.getMostSignificantBits(), leastSigBits = uuid.getLeastSignificantBits();
		return digits(mostSigBits >> 32, 8)+ digits(mostSigBits >> 16, 4)
				+ digits(mostSigBits, 4) + digits(leastSigBits >> 48, 4) + digits(leastSigBits, 12);
        
	}
	
	public static String replaceAll(String text, char splitChar, String target) {
		boolean empty = target == null || target.isEmpty();
		StringBuilder buf = new StringBuilder(text.length() + (empty ? 0 : 128 * target.length()));
		for (char ch : text.toCharArray()) {
			if (ch == splitChar) {
				if (!empty) {
					buf.append(target);
				}
			} else {
				buf.append(ch);
			}
		}
		return buf.toString();
	}
	
	
	 /**
     * 多参数判空
     * @author: Lu Yang
     * @date: 2019-05-07 16:27
     * @param obj
     * @return: boolean
     */
	public static boolean isEmpty (Object ...obj) {
		for (int i = 0 ; i < obj.length ; i ++) {
			if (StringUtil.isEmpty(obj[i])) {
				return true;
			}
		}
		return false;
	}
	
	public static String getChangeMobile(String mobile){
		if(StringUtil.isEmpty(mobile)){
			return "";
		}
		return mobile.replace(mobile.substring(3, 8), "*****");
		// return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2");
	}
}
