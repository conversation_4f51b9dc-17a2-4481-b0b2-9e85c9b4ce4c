package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_device")
@ApiModel(value = "DeviceEntity对象", description = "DeviceEntity对象")
public class DeviceEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备标识uuid")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "设备名称", required = true)
    private String name;

    @ApiModelProperty(value = "设备ip", required = true)
    private String ip;

    @ApiModelProperty(value = "设备端口", required = true)
    private Integer port;

    @ApiModelProperty(value = "0未连接1已连接2未连接", required = true)
    private Integer status;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @TableField(exist = false)
    @ApiModelProperty(value = "运行状态。true为运行。false为停止")
    private Boolean runStatus;

    @ApiModelProperty(value = "设备二维码code")
    private String code;

    @ApiModelProperty(value = "电量输出初始值")
    private Double outputHistoryInit;

    @ApiModelProperty(value = "电量输入初始值")
    private Double inputHistoryInit;

    @ApiModelProperty(value = "是否虚拟,默认不虚拟false")
    private Boolean unreal;

    @ApiModelProperty(value = "虚拟设备的pcs序号,0,1,2,3,4")
    private Integer pcsIndex;

    @ApiModelProperty(value = "功率分配百分比")
    private Float powerCapacityPercent;

    @ApiModelProperty(value = "是否维护,默认不维护false")
    private Boolean maintain;

    @ApiModelProperty(value = "是否显示gps，默认不显示false")
    private Boolean showGps;

    @ApiModelProperty(value = "是否计算收益,默认不维护false")
    private Boolean income;

    @ApiModelProperty(value = "序号")
    @TableField(value = "`index`")
    private Integer index;

    // ------ add for dcdc 2024-01-10 16:04:21------

    @ApiModelProperty(value = "是否有STS")
    private Boolean hasSts;

    @ApiModelProperty(value = "是否有DCDC")
    private Boolean hasDcdc;

    @ApiModelProperty(value = "pv表初始放电量初始值")
    private Double pvOutputHistoryInit;

    @ApiModelProperty(value = "pv表初始充电量初始值")
    private Double pvInputHistoryInit;
}
