package com.wifochina.modules.group.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.vo.GroupGoVo;
import com.wifochina.modules.group.vo.GroupVO;

import java.util.List;

/**
 * 分组 服务类
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface GroupService extends IService<GroupEntity> {

    /**
     * 分页查询接口
     *
     * @return : IPage<GroupVo>
     */
    IPage<GroupVO> queryGroupVO(IPage<GroupVO> groupPage, String name, String projectId);

    /**
     * 查询某个项目 id 的所有分组 , projectId = WebUtils.projectId.get()
     *
     * @return : List<GroupEntity>
     */
    List<GroupGoVo> queryGroup(String projectId);

    /**
     * 查询系统分组
     *
     * @return : GroupEntity
     */
    GroupEntity systemGroupEntity(String projectId);

    /**
     * 查询非系统分组列表
     *
     * @return : List<GroupEntity>
     */
    List<GroupEntity> queryGroupsNotSystem(String projectId);

    List<GroupEntity> queryGroupsNotSystemAndBindIncomeDeviceOrMeter(String projectId);
}
