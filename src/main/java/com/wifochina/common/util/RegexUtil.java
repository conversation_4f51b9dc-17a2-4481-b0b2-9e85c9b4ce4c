package com.wifochina.common.util;

import java.util.regex.Pattern;

/**
 * @description: RegexUtil
 * @date: 6/1/2022 9:02 AM
 * @author: jacob.sun
 * @version: 1.0
 */
public class RegexUtil {

    /**
     * Ip正则表达式校验
     * @param content
     * @return
     */
    public static boolean isIp(String content) {
        String pattern = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";
        boolean isMatch = Pattern.matches(pattern, content);
        return isMatch;
    }

}
