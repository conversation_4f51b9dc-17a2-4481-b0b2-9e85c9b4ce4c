spring:
  datasource:
    url: jdbc:${DATABASE_HOST:mysql://**************:3306}/${DATABASE_NAME:ems}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull
    username: ${DATABASE_USERNAME:root}
    password: ${DATABASE_PASSWORD:123@abcd}
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: ${REDIS_HOST:**************}
    port: ${REDIS_PORT:6379}
    #password: ${REDIS_PASSWORD:wq2018!}
    database: ${REDIS_DATABASE:8}
    timeout: 10000
    jedis:
      pool:
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 1
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  # 定时任务线程池大小
  task:
    #T0 主要是系统设备数量，需量等不经常变化的值
    t0:
      fixedDelay: 60000
    #T1，T1+ 系统运行关键点位数据
    t1:
      fixedDelay: 30000
    #T2 电池簇状态
    t2:
      fixedDelay: 60000
    #事件采集数据
    event:
      fixedDelay: 30000
    scheduling:
      pool:
        size: 20
      thread-name-prefix: task-scedule

ems:
  go:
    #协调器url
    url:  ${GO_URL:http://**************:8080}

  collect:
    data:
      url: ${DATA_URL:http://{ip}:{port}/api/modbus?start={start}&count={count}&type={type}}
      #一次采集数据最大值，默认10000
      count: 65535
  swagger2:
    enable: ${SWAGGER_ENABLE:true}
    basePackage: com.wifochina
    basePath:
    title: 储能EMS
    description: 储能EMS接口文档
    termsOfServiceUrl: http://localhost:8808/api/swagger-ui.html
    version: 2.0
    apiName: Authorization
    apiKeyName: Authorization
swagger:
  enable: ${SWAGGER_ENABLE:true}




