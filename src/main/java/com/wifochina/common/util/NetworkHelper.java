package com.wifochina.common.util;

import java.io.IOException;
import java.net.*;
import java.util.Enumeration;

/**
 * @Author: jacob
 * Date: 2022-03-03 00:06:18
 * 测试网络连通性
 * 致敬大师，彼岸无岸，当下即是
 */
public class NetworkHelper {

    private static NetworkHelper instance = new NetworkHelper();

    public static NetworkHelper getInstance() {
        return instance;
    }

    /**
     *
     * 测试本地能否ping ip
     *  @param ip
     *  @return
     */
    public boolean isReachIp(String ip) {
        boolean isReach = false;
        try {
                InetAddress address = InetAddress.getByName(ip);
                // ping this IP
                int timeout = 5000;
                if (address.isReachable(timeout)) {
                    isReach = true;
                } else {
                    isReach = false;
                }
            }
        catch (Exception e) {
            }
        return isReach;
        }

    /**
     * 测试本地所有的网卡地址都能ping通 ip
     * @param ip
     * @return
     */
    public boolean isReachNetworkInterfaces(String ip) {
        boolean isReach = false;
        try {
            InetAddress address = InetAddress.getByName(ip);
            // ping this IP
            int timeout = 5000;
            if (address.isReachable(timeout)) {
                isReach = true;
            } else {
                isReach = false;
            }
            if (isReach) {
                //Trying different interfaces
                Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
                while (netInterfaces.hasMoreElements()) {
                    NetworkInterface ni = netInterfaces.nextElement();
                    //"Checking interface, DisplayName:" + ni.getDisplayName() + ", Name:" + ni.getName());
                    if (address.isReachable(ni, 0, 5000)) {
                        isReach = true;
                    } else {
                        isReach = false;
                    }
                    Enumeration<InetAddress> ips = ni.getInetAddresses();
                    while (ips.hasMoreElements()) {
                        ips.nextElement().getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
        }
        return isReach;
    }

    /**
     * 获取能与远程主机指定端口建立连接的本机ip地址
     * @param remoteAddr
     * @param port
     * @return
     */
    public String getReachableIp(InetAddress remoteAddr, int port) {
        String retIp = null;
        Enumeration<NetworkInterface> netInterfaces;
        try {
            netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> localAddrs = ni.getInetAddresses();
                while (localAddrs.hasMoreElements()) {
                    InetAddress localAddr = localAddrs.nextElement();
                    if (isReachable(localAddr, remoteAddr, port, 5000)) {
                        retIp = localAddr.getHostAddress();
                        break;
                    }
                }
            }
        } catch (SocketException e) {
           //"Error occurred while listing all the local network addresses:" + e.getMessage());
        }
        if (retIp == null) {
          //"NULL reachable local IP is found!");
        } else {
          //"Reachable local IP is found, it is " + retIP);
        }
        return retIp;
    }

    /**
     * 获取能与远程主机指定端口建立连接的本机ip地址
     * @param remoteIp
     * @param port
     * @return
     */
    public Boolean isReachIpAndPort(String remoteIp, int port) {
        boolean isReach = false;

        InetAddress remoteAddr = null;
        Enumeration<NetworkInterface> netInterfaces;
        try {
            remoteAddr = InetAddress.getByName(remoteIp);
            netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> localAddrs = ni.getInetAddresses();
                while (localAddrs.hasMoreElements()) {
                    InetAddress localAddr = localAddrs.nextElement();
                    if (isReachable(localAddr, remoteAddr, port, 10000)) {
                        isReach = true;
                        break;
                    }
                }
            }
        } catch (UnknownHostException e) {
            //"Error occurred while listing all the local network addresses:" + e.getMessage());
        } catch (SocketException e) {
            //"Error occurred while listing all the local network addresses:" + e.getMessage());
        }

        return isReach;
    }

    /**
     * 获取能与远程主机指定端口建立连接的本机ip地址
     * @param remoteIp
     * @param port
     * @return
     */
    public String getReachableIp(String remoteIp, int port) {
        String retIp = null;
        InetAddress remoteAddr = null;
        Enumeration<NetworkInterface> netInterfaces;
        try {
            remoteAddr = InetAddress.getByName(remoteIp);
            netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> localAddrs = ni.getInetAddresses();
                while (localAddrs.hasMoreElements()) {
                    InetAddress localAddr = localAddrs.nextElement();
                    if (isReachable(localAddr, remoteAddr, port, 20000)) {
                        retIp = localAddr.getHostAddress();
                        break;
                    }
                }
            }
        } catch (UnknownHostException e) {
            //"Error occurred while listing all the local network addresses:" + e.getMessage());
        } catch (SocketException e) {
            //"Error occurred while listing all the local network addresses:" + e.getMessage());
        }
        if (retIp == null) {
            //"NULL reachable local IP is found!");
        } else {
            //"Reachable local IP is found, it is " + retIP);
        }
        return retIp;
    }

    /**
     * 测试local InetAddr能否与远程的主机指定端口建立连接相连
     * @param localInetAddr
     * @param remoteInetAddr
     * @param port
     * @param timeout
     * @return
     **/
    public boolean isReachable(InetAddress localInetAddr, InetAddress remoteInetAddr, int port, int timeout) {
        boolean isReachable = false;
        Socket socket = null;
        try{
            // 端口号设置为 0 表示在本地挑选一个可用端口进行连接
            socket = new Socket();          
            SocketAddress localSocketAddr = new InetSocketAddress(localInetAddr, 0);
            socket.bind(localSocketAddr);
            InetSocketAddress endpointSocketAddr = new InetSocketAddress(remoteInetAddr, port);
            socket.connect(endpointSocketAddr, timeout);
           //"SUCCESS - connection established! Local: " + localInetAddr.getHostAddress() + " remote: " + remoteInetAddr.getHostAddress() + " port" + port);
            isReachable = true;
        } catch (IOException e) {
            //"FAILRE - CAN not connect! Local: " + localInetAddr.getHostAddress() + " remote: " + remoteInetAddr.getHostAddress() + " port" + port);
        } finally {
            if (socket != null) {
                try {
                    socket.close();
                } catch (IOException e) {
                   //"Error occurred while closing socket:" + e.getMessage());
                }
            }
        }
        return isReachable;
    }

    /**
     * 测试localIp能否与远程的主机指定端口建立连接相连
     * @param localIp
     * @param remoteIp
     * @param port
     * @param timeout
     * @return
     */
    public boolean isReachable(String localIp, String remoteIp, int port, int timeout) {
        boolean isReachable = false;
        Socket socket = null;
        InetAddress localInetAddr = null;
        InetAddress remoteInetAddr = null;
        try {
            localInetAddr = InetAddress.getByName(localIp);
            remoteInetAddr = InetAddress.getByName(remoteIp);  
            // 端口号设置为 0 表示在本地挑选一个可用端口进行连接
            socket = new Socket();          
            SocketAddress localSocketAddr = new InetSocketAddress(localInetAddr, 0);
            socket.bind(localSocketAddr);
            InetSocketAddress endpointSocketAddr = new InetSocketAddress(remoteInetAddr, port);
            socket.connect(endpointSocketAddr, timeout);
            //"SUCCESS - connection established! Local: " + localInetAddr.getHostAddress() + " remote: " + remoteInetAddr.getHostAddress() + " port" + port);
            isReachable = true;
        } catch (IOException e) {
            //"FAILRE - CAN not connect! Local: " + localInetAddr.getHostAddress() + " remote: " + remoteInetAddr.getHostAddress() + " port" + port);
        } finally {
            if (socket != null) {
                try {
                    socket.close();
                } catch (IOException e) {
                    //"Error occurred while closing socket:" + e.getMessage());
                }
            }
        }
        return isReachable;
    }

    public static void main(String[] args) {
        String ip = "**************";
        if (NetworkHelper.getInstance().isReachIp(ip)) {
            System.out.println("=======本机可以ping通ip：" + "**************");
        } else {
            System.out.println("=======本机ping不通ip：" + "**************");
        }
//        if (NetworkHelper.getInstance().isReachNetworkInterfaces("**************")) {
//            System.out.println("=======本机所有网卡可以ping通ip：" + "**************");
//        } else {
//            System.out.println("=======本机所有网卡ping不通ip：" + "**************");
//        }
        boolean canConnect = NetworkHelper.getInstance().isReachIpAndPort("**************", 3306);
        if (canConnect) {
            System.out.println("=======本机可以与ip：" + "**************" + ",port:" + 3306 + "建立连接：");
        } else {
            System.out.println("=======本机不能与ip：" + "**************" + ",port:" + 3306 + "建立连接");
        }
    }
}
