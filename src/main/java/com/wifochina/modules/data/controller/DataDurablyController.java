package com.wifochina.modules.data.controller;

import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.page.Result;
import com.wifochina.common.util.HashUtil;
import com.wifochina.modules.data.influxdb.InfluxConfig;
import com.wifochina.modules.data.request.PersistenceRequest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@RestController
@Api(tags = "data收集")
@RequestMapping("/data")
@Slf4j
public class DataDurablyController {

    @Resource private RestTemplate restTemplate;

    @Resource private InfluxConfig influxConfig;

    private String realtimeBucket;

    private String foreverBucket;

    private String meanBucket;

    private String queryUrl;

    @PostConstruct
    public void init() {
        this.realtimeBucket = influxConfig.getRealtimeBucket();
        this.foreverBucket = influxConfig.getForeverBucket();
        this.meanBucket = influxConfig.getMeanBucket();
        this.queryUrl =
                influxConfig.getUrl()
                        + "/api/v2/query?org=weiheng&queryInAllDB=true&ignoreResult=true";
    }

    private static final List<Restrictions> emsColumns =
            Stream.of(
                            "ems_ac_active_power_pos",
                            "ems_ac_reactive_power",
                            "ems_ac_active_power_neg",
                            "ems_ac_active_power")
                    .map(e -> Restrictions.field().equal(e))
                    .collect(Collectors.toList());
    private static final List<Restrictions> meterColumns =
            Stream.of(
                            "ac_active_power",
                            "ac_reactive_power",
                            "ac_current",
                            "ac_currents_0",
                            "ac_currents_1",
                            "ac_currents_2",
                            "ac_active_powers_0",
                            "ac_active_powers_1",
                            "ac_active_powers_2",
                            "ac_reactive_powers_0",
                            "ac_reactive_powers_1",
                            "ac_reactive_powers_2",
                            "dc_voltage",
                            "dc_power",
                            "frequency",
                            "ac_voltage",
                            "ac_voltages_0",
                            "ac_voltages_1",
                            "ac_voltages_2")
                    .map(e -> Restrictions.field().equal(e))
                    .collect(Collectors.toList());

    @PostMapping("/persistentData")
    @ApiOperation("追补数据")
    public Result<T> PersistentData(@RequestBody PersistenceRequest persistenceRequest) {

        long start = persistenceRequest.getStart();
        long end = persistenceRequest.getEnd();
        long interval = 15 * 60; // 15分钟的时间间隔，单位为毫秒

        for (long current = start; current <= end; current += interval) {
            // 输出结果
            log.info("{} -->正在执行", "PersistentData");
            persistentEmsOrMeterData(persistenceRequest, "ems100");
            persistentEmsOrMeterData(persistenceRequest, "meter");
            persistentNonEmsOrMeterData(persistenceRequest, "ems100_cell");
            persistentNonEmsOrMeterData(persistenceRequest, "group");
            persistentMeanData(persistenceRequest, "ems100", emsColumns);
            persistentMeanData(persistenceRequest, "meter", meterColumns);
        }
        return Result.success();
    }

    public void persistentEmsOrMeterData(PersistenceRequest persistenceRequest, String tableName) {
        persistentRealTimeData(persistenceRequest, tableName, 1L);
    }

    public void persistentNonEmsOrMeterData(
            PersistenceRequest persistenceRequest, String tableName) {
        persistentRealTimeData(persistenceRequest, tableName, 5L);
    }

    public void persistentRealTimeData(
            PersistenceRequest persistenceRequest, String tableName, Long windowPeriod) {
        Flux flux =
                Flux.from(this.realtimeBucket)
                        .range(persistenceRequest.getStart(), persistenceRequest.getEnd())
                        .filter(
                                Restrictions.tag("projectId")
                                        .equal(persistenceRequest.getProjectId()))
                        .filter(
                                Restrictions.measurement()
                                        .equal(
                                                tableName.trim()
                                                        + "@"
                                                        + HashUtil.elfHash(
                                                                persistenceRequest.getProjectId())))
                        .aggregateWindow(windowPeriod, ChronoUnit.MINUTES, "last")
                        .to(this.foreverBucket);
        log.info("forever persistent -- >{}", flux);
        restTemplate.postForEntity(this.queryUrl, flux.toString(), String.class);
    }

    public void persistentMeanData(
            PersistenceRequest persistenceRequest,
            String tableName,
            List<Restrictions> restrictions) {
        Flux flux =
                Flux.from(this.realtimeBucket)
                        .range(persistenceRequest.getStart(), persistenceRequest.getEnd())
                        .filter(
                                Restrictions.tag("projectId")
                                        .equal(persistenceRequest.getProjectId()))
                        .filter(
                                Restrictions.measurement()
                                        .equal(
                                                tableName
                                                        + "@"
                                                        + HashUtil.elfHash(
                                                                persistenceRequest.getProjectId())))
                        .filter(Restrictions.or(restrictions.toArray(new Restrictions[] {})))
                        .aggregateWindow(1L, ChronoUnit.MINUTES, "mean")
                        .to(this.meanBucket);
        log.info("mean persistent -- >{}", flux);
        restTemplate.postForEntity(this.queryUrl, flux.toString(), String.class);
    }
}
