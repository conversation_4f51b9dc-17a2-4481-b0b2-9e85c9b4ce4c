package com.wifochina.common.runner;

import com.wifochina.common.delay.DelayQueueMessage;
import com.wifochina.common.delay.MqService;
import java.util.concurrent.DelayQueue;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/8/7 19:21
 * @version 1.0
 */
@Component
@Order(100)
@Slf4j
public class DelayQueueInit implements ApplicationRunner {

    public static final DelayQueue<DelayQueueMessage> DELAY_QUEUE = new DelayQueue<>();

    @Resource
    private MqService mqService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public void run(ApplicationArguments args) {
        threadPoolTaskExecutor.execute(() -> {
            log.info("InfiniteLoop DelayQueue start");
            for (; ; ) {
                DelayQueueMessage delayQueueMessage;
                try {
                    delayQueueMessage = DELAY_QUEUE.take();
                    threadPoolTaskExecutor.execute(() -> mqService.consume(delayQueueMessage));
                } catch (InterruptedException e) {
                    log.error("InfiniteLoop DelayQueue error", e);
                }
            }
        });
    }
}
