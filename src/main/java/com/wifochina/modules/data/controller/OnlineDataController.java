package com.wifochina.modules.data.controller;

import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.CommonConstants;
import com.wifochina.common.delay.DelayQueueMessage;
import com.wifochina.common.page.Result;
import com.wifochina.common.runner.DelayQueueInit;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.alarm.service.AlarmHandler;
import com.wifochina.modules.data.listener.OnlineStateChange;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.CameraEntity;
import com.wifochina.modules.group.entity.ControllableEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.CameraService;
import com.wifochina.modules.group.service.ControllableService;
import com.wifochina.modules.group.service.DeviceService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.formula.functions.T;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @since 2024-03-22 6:01 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "data收集")
@RequestMapping("/data")
@RequiredArgsConstructor
public class OnlineDataController {

    private final EventMessageService eventMessageService;

    private final DeviceService deviceService;

    private final AmmeterService ammeterService;

    private final CameraService cameraService;

    private final ControllableService controllableService;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final AlarmHandler alarmHandler;

    private final StringRedisTemplate stringRedisTemplate;

    @PostMapping("/onlineChange")
    @ApiOperation("离线事件")
    public Result<T> collectDeviceDataPoint(@RequestBody OnlineStateChange onlineStateChange) {
        onlineStateChange.setTime(
                String.valueOf(onlineStateChange.getTime()).length() == 13
                        ? onlineStateChange.getTime()
                        : onlineStateChange.getTime() * 1000);
        log.info("{} -->正在执行--{}", "离线事件", onlineStateChange);
        if (onlineStateChange.getDeviceIds().isEmpty()) {
            return Result.success();
        }
        long time = onlineStateChange.getTime();
        Map<String, Boolean> statusChangeVoMap = onlineStateChange.getDeviceIds();
        List<String> deviceIds = new ArrayList<>(statusChangeVoMap.keySet());
        List<DeviceEntity> deviceEntities =
                deviceService.lambdaQuery().in(DeviceEntity::getId, deviceIds).list();
        saveEmsEvent(deviceEntities, statusChangeVoMap, time);
        List<AmmeterEntity> ammeterEntities =
                ammeterService.lambdaQuery().in(AmmeterEntity::getId, deviceIds).list();
        saveMeterEvent(ammeterEntities, statusChangeVoMap, time);
        List<CameraEntity> cameraEntities =
                cameraService.lambdaQuery().in(CameraEntity::getId, deviceIds).list();
        saveCameraEvent(cameraEntities, statusChangeVoMap, time);
        List<ControllableEntity> controllableEntities =
                controllableService.lambdaQuery().in(ControllableEntity::getId, deviceIds).list();
        saveSteerableEvent(controllableEntities, statusChangeVoMap, time);
        return Result.success();
    }

    private void saveCameraEvent(
            List<CameraEntity> cameraList, Map<String, Boolean> statusChangeVoMap, Long time) {
        cameraList.forEach(
                camera -> {
                    log.info("online check ---> camera数据改变，uuid:{}", camera.getId());
                    EventMessageEntity eventMessageEntity = new EventMessageEntity();
                    eventMessageEntity.setDeviceId(camera.getId());
                    eventMessageEntity.setEquipName("CAMERA");
                    eventMessageEntity.setMaintain(false);
                    eventMessageEntity.setEquipType("CAMERA");
                    eventMessageEntity.setProjectId(camera.getProjectId());
                    eventMessageEntity.setCreateTime(time);
                    eventMessageEntity.setUpdateTime(time);
                    handOffLineEvent(eventMessageEntity, statusChangeVoMap.get(camera.getId()));
                });
    }

    private void saveSteerableEvent(
            List<ControllableEntity> controllableList,
            Map<String, Boolean> statusChangeVoMap,
            Long time) {
        controllableList.forEach(
                controllable -> {
                    log.info("online check ---> controllable数据改变，uuid:{}", controllable.getId());
                    EventMessageEntity eventMessageEntity = new EventMessageEntity();
                    eventMessageEntity.setDeviceId(controllable.getId());
                    eventMessageEntity.setEquipName("CONTROLLABLE");
                    eventMessageEntity.setMaintain(false);
                    eventMessageEntity.setEquipType("CONTROLLABLE");
                    eventMessageEntity.setProjectId(controllable.getProjectId());
                    eventMessageEntity.setCreateTime(time);
                    eventMessageEntity.setUpdateTime(time);
                    handOffLineEvent(
                            eventMessageEntity, statusChangeVoMap.get(controllable.getId()));
                });
    }

    private void saveMeterEvent(
            List<AmmeterEntity> meterList, Map<String, Boolean> statusChangeVoMap, Long time) {
        meterList.forEach(
                meter -> {
                    log.info("online check --->  meter数据改变，uuid:{}", meter.getId());
                    EventMessageEntity eventMessageEntity = new EventMessageEntity();
                    eventMessageEntity.setDeviceId(meter.getId());
                    eventMessageEntity.setEquipName("METER");
                    eventMessageEntity.setMaintain(meter.getMaintain());
                    eventMessageEntity.setEquipType("METER");
                    eventMessageEntity.setProjectId(meter.getProjectId());
                    eventMessageEntity.setCreateTime(time);
                    eventMessageEntity.setUpdateTime(time);
                    handOffLineEvent(eventMessageEntity, statusChangeVoMap.get(meter.getId()));
                });
    }

    private void saveEmsEvent(
            List<DeviceEntity> emsList, Map<String, Boolean> statusChangeVoMap, Long time) {
        emsList.forEach(
                ems -> {
                    EventMessageEntity eventMessageEntity = new EventMessageEntity();
                    eventMessageEntity.setDeviceId(ems.getId());
                    eventMessageEntity.setEquipName("EMS");
                    eventMessageEntity.setMaintain(ems.getMaintain());
                    eventMessageEntity.setEquipType("EMS");
                    eventMessageEntity.setProjectId(ems.getProjectId());
                    eventMessageEntity.setCreateTime(time);
                    eventMessageEntity.setUpdateTime(time);
                    handOffLineEvent(eventMessageEntity, statusChangeVoMap.get(ems.getId()));
                    log.info("online check ---> ems数据改变，uuid:{}", ems.getId());
                });
    }

    public void handOffLineEvent(EventMessageEntity eventMessageEntity, Boolean oneLine) {
        if (Boolean.FALSE.equals(oneLine)) {
            eventMessageEntity.setEventOnOff("on");
            eventMessageEntity.setStatus(0);
        } else {
            eventMessageEntity.setEventOnOff("off");
            eventMessageEntity.setStatus(1);
        }
        eventMessageEntity.setEventDescription("离线");
        eventMessageEntity.setEventDescriptionEn("offline");
        // 事件编码=偏移量+bit码
        // 事件编码=typeCode_eventType_偏移量+bit码
        eventMessageEntity.setEventCode(0);
        eventMessageEntity.setEventType(EventLevelEnum.FAULT.getLevel());
        eventMessageEntity.setEventKey(-1 + "_" + "OFFLINE" + "_" + 0);
        // 保存事件数据
        eventMessageService.save(eventMessageEntity);
        // 走离线告警逻辑
        threadPoolTaskExecutor.execute(() -> handOnOffLineEvent(eventMessageEntity));
    }

    /**
     * 处理离线事件
     *
     * @param eventMessage
     */
    private void handOnOffLineEvent(EventMessageEntity eventMessage) {
        // 获取告警配置缓存
        AlarmCacheDTO cacheItem = alarmHandler.cacheAlarmInfo(eventMessage.getProjectId());
        if (CollectionUtils.isEmpty(cacheItem.getAlarmConfigList())) {
            return;
        }
        // 校验离线告警类型是否开启
        AlarmConfigEntity offlineConfig = cacheItem.getAlarmConfigList().stream()
                .filter(i -> AlarmContentEnum.DEVICE_OFFLINE.getCode() == i.getAlarmContent() &&
                        i.getIsEnabled())
                .findFirst()
                .orElse(null);
        if (offlineConfig == null) {
            return;
        }
        // 写入缓存
        String redisKey = CommonConstants.buildOfflineRedisKey(eventMessage.getProjectId(), eventMessage.getEventKey());
        long time = offlineConfig.getOfflineTimeThreshold();
        stringRedisTemplate.opsForValue().set(redisKey, "", 3 * time, TimeUnit.MINUTES);
        // 推送消息到队列
        DelayQueueInit.DELAY_QUEUE.offer(DelayQueueMessage.builder()
                .eventRedisKey(redisKey)
                .eventMessage(eventMessage)
                .delayTime(System.currentTimeMillis() + time * 60 * 1000)
                .setTime(time)
                .turns(1)
                .build());
    }
}
