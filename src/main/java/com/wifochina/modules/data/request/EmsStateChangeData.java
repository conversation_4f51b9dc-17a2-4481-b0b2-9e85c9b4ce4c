package com.wifochina.modules.data.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmsStateChangeData {

    @JsonProperty("ems_bit_codes")
    private AllBitCode emsBitCodes;

    @JsonProperty("bms_bit_codes")
    private AllBitCode bmsBitCodes;

    @JsonProperty("bms_cluster_bit_codes")
    private List<AllBitCode> bmsClusterBitCodes;

    @JsonProperty("pcs_bit_codes")
    private List<AllBitCode> pcsBitCodes;

    @JsonProperty("dcdc_bit_codes")
    private List<AllBitCode> dcdcBitCodes;

    @JsonProperty("state_bit_codes")
    private List<AllBitCode> stateBitCodes;

    @JsonProperty("air_conditioner_bit_codes")
    private List<NoAlarmBitCode> airConditionerBitCodes;

    @JsonProperty("firefighting_bit_codes")
    private List<NoAlarmBitCode> firefightingBitCodes;

    @JsonProperty("water_cooler_bit_codes")
    private List<NoAlarmBitCode> waterCoolerBitCodes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class AllBitCode {
        @JsonProperty("DeviceTypeCode")
        private int deviceTypeCode;

        @JsonProperty("StateCode")
        private List<Integer> stateCode;

        @JsonProperty("AlarmCode")
        private List<Integer> alarmCode;

        @JsonProperty("FaultCode")
        private List<Integer> faultCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class NoAlarmBitCode {
        @JsonProperty("DeviceTypeCode")
        private int deviceTypeCode;

        @JsonProperty("StateCode")
        private List<Integer> stateCode;

        @JsonProperty("AlarmCode")
        private List<Integer> faultCode;
    }
}
