package com.wifochina.modules.data.entity;

import lombok.Data;

import java.util.List;

/**
 * @description: MeterData
 * @date: 4/25/2022 3:57 PM
 * @author: jacob.sun
 * @version: 1.0
 */

@Data
public class MeterContentData {

     private Boolean online;

     private Boolean data_invalid;

     private Integer device_type_code;

     private Double dc_voltage;

     private Double dc_current;

     private Double dc_power;

     private Double dc_history_positive_power_in_kwh;

     private Double dc_history_negative_power_in_kwh;

     private Double ac_voltage;

     private Double ac_current;

     //有功功率
     private Double ac_active_power;

     //无功功率
     private Double ac_reactive_power;

     private List<Double> ac_voltages;

     private List<Double> ac_currents;

     private List<Double> ac_active_powers;

     private List<Double> ac_reactive_powers;

     //差值算运营收益
     private Double ac_history_positive_power_in_kwh;

     //差值算运营收益
     private Double ac_history_negative_power_in_kwh;

     private List<Boolean> aux_digital;

     private List<Double> aux_analog;

     private Double  frequency;

     private Boolean is_dc;

}
