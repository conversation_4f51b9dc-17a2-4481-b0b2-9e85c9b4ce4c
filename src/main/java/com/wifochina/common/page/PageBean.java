package com.wifochina.common.page;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: PageRequest
 * @date: 2022/3/15 9:21
 * @author: jacob.sun
 * @version: 1.0
 */
@Data
@ApiModel("分页实体")
@NoArgsConstructor
@AllArgsConstructor
public class PageBean {

    /**
     * 当前页码
     * */
    @ApiModelProperty("当前页码")
    @JsonProperty("pageNum")
    private int pageNum = 1;

    /**
     * 每页数量
     * */
    @ApiModelProperty("每页数量")
    @JsonProperty("pageSize")
    private int pageSize = 10;
}
