package com.wifochina.modules.data.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @description: StateChangeRequest
 * @date: 7/15/2022 1:05 PM
 * @author: jacob.sun
 * @version: 1.0
 */

@Data
public class StateChangeRequest {
    @ApiModelProperty(value = "设备id", required = true)
    private String deviceId;

    @ApiModelProperty(value = "项目id", required = true)
    private String projectId;

    @ApiModelProperty(value = "当前数据", required = true)
    private  int[] currentData;

    @ApiModelProperty(value = "变化之前的数据", required = true)
    private  int[] lastData;

    @ApiModelProperty(value = "采集时间", required = true)
    private long time;
}
