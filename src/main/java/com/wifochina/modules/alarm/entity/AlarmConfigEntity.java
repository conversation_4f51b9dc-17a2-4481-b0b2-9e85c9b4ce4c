package com.wifochina.modules.alarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 告警配置实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_alarm_config")
@ApiModel(value = "AlarmConfigEntity对象", description = "告警配置")
public class AlarmConfigEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "告警内容")
    private Integer alarmContent;

    @ApiModelProperty(value = "是否启用(0:禁用 1:启用)")
    private Boolean isEnabled;

    @ApiModelProperty(value = "收益偏差系数")
    private Float profitDeviationCoefficient;

    @ApiModelProperty(value = "停机时长阈值(分钟)")
    private Integer downtimeThreshold;

    @ApiModelProperty(value = "离线状态持续时间(分钟)")
    private Integer offlineTimeThreshold;

    @ApiModelProperty(value = "效率提醒阈值")
    private Float efficiencyReminderThreshold;
}
