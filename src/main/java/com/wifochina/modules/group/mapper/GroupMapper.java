package com.wifochina.modules.group.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.vo.GroupGoVo;
import com.wifochina.modules.group.vo.GroupVO;

import java.util.List;

/**
 * 分组 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
public interface GroupMapper extends BaseMapper<GroupEntity> {
    IPage<GroupVO> queryGroupVO(IPage<GroupVO> groupPage, String name, String projectId);

    List<GroupGoVo> queryGroup(String projectId);

    List<GroupEntity> queryGroupsNotSystemAndBindIncomeDeviceOrMeter(String projectId);
}
