package com.wifochina;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import javax.annotation.PostConstruct;
import java.util.TimeZone;

/**
 * @description: Application
 * EnableCaching 仅仅在oauth2客户端基于数据的缓存用到，见DatabaseCachableClientDetailsService
 * @date: 2022/3/13 1:09
 * @author: jacob.sun
 * @version: 1.0
 */
@EnableKnife4j
@EnableAspectJAutoProxy
@SpringBootApplication(scanBasePackages = {"com.wifochina", "com.weihengtech.alert"})
public class Application {

    @PostConstruct
    void started()
    {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class);
    }
}
