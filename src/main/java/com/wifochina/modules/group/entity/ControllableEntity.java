package com.wifochina.modules.group.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 协调控制器
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Getter
@Setter
@TableName("t_controllable")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Controllable对象")
public class ControllableEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("uuid")
    private String id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型：Load、IO、Power")
    private String type;

    @ApiModelProperty("型号")
    private String vendor;

    @ApiModelProperty(value = "1已连接、2未连接、0未测试")
    private Integer status;

    @ApiModelProperty("ip")
    private String ip;

    @ApiModelProperty("端口")
    private Integer port;

    @ApiModelProperty("配置")
    private String config;

    @ApiModelProperty("项目id")
    private String projectId;

    @TableField(exist = false)
    @ApiModelProperty(value = "是否维护,默认不维护false")
    private Boolean maintain = false;
}
