package com.wifochina.common.file.storage;
import org.apache.poi.util.IOUtils;

import com.wifochina.common.util.StringUtil;

import java.io.*;

/**
 * 本地文件上传
 * <AUTHOR>
 */
public class LocalFileStorage implements FileStorage {

    private String path;
    
    private String siteUrl;

    private Boolean haveCreatePath = false;

    public LocalFileStorage(String path, String siteUrl) {
        this.path = path;
        this.siteUrl = siteUrl;
    }

    @Override
    public String store(byte[] fileBytes, String key) {
        path = getFileUploadPath();
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream(new File(path + key));
            fileOutputStream.write(fileBytes);
        } catch (IOException e) {
            throw new RuntimeException("Write file error!");
        } finally {
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    throw new RuntimeException("Write file error!");
                }
            }
        }
        return key;
    }

    @Override
    public String store(InputStream input, String key) {
    	
    	path = getFileUploadPath();
        File file = new File(path,key);
        if(file.exists()){
            throw new IllegalArgumentException("相同KEY的文件已存在");
        }
        FileOutputStream output = null;
        try {
            file.createNewFile();
            output =  new FileOutputStream(file);
            //maybe changed
            IOUtils.copy(input,output);
            output.flush();
        } catch (IOException e) {
            throw new RuntimeException("存储文件失败：" + e);
        }finally {
            if(output != null){
                try {
                    output.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return key;
    }

    @Override
    public byte[] getBytes(String key) {
        String filePath = getFileUploadPath() + key;
        File file = new File(filePath);
        FileInputStream fileInputStream = null;
        byte[] bytes = new byte[(int) file.length()];
        try {
            fileInputStream = new FileInputStream(file);
            fileInputStream.read(bytes);
            return bytes;
        } catch (IOException e) {
            throw new RuntimeException("Read file error!");
        } finally {
            try {
                fileInputStream.close();
            } catch (IOException e) {
                throw new RuntimeException("Read file error!");
            }
        }
    }

    @Override
    public void remove(String key) {
        new File(this.getFileUploadPath(),key).delete();
    }

    @Override
    public InputStream getInputStream(String key) {
        try {
            return new FileInputStream(new File(this.getFileUploadPath(),key));
        } catch (FileNotFoundException e) {
            throw new RuntimeException("Read file error!");
        }
    }

    @Override
    public String getDownloadUrl(String filePath) {
    	if (!StringUtil.isEmpty(siteUrl)) {
    		return siteUrl + filePath;
    	}
        return filePath;
    }

    private String getFileUploadPath() {
        //如果没有写文件上传路径,保存到临时目录
        if (StringUtil.isEmpty(path)) {
            return System.getProperty("java.io.tmpdir");
        } else {
            //判断有没有结尾符,没有得加上
            if (!path.endsWith(File.separator)) {
                path = path + File.separator;
            }
            //判断目录存不存在
            if (!haveCreatePath) {
                File file = new File(path);
                file.mkdirs();
                haveCreatePath = true;
            }
            return path;
        }
    }

	@Override
	public UploadResult upload(BytesFile bytesFile, String key) {
		// TODO Auto-generated method stub
		return null;
	}

}
