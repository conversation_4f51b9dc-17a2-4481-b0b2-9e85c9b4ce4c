package com.wifochina.modules.data.entity;

import lombok.Getter;

/**
 * @since  4/1/2022 9:12 AM
 * <AUTHOR>
 * @version 1.0
 */
@Getter
public enum OffsetTypeEnum {
    /**
     * 空调
     */
    AIR("AIR","air_conditioner_count"),

    /**
     * 消防
     */
    FIRE("FIRE","firefighting_count"),

    /**
     * 水冷
     */
    WATER_COOLER("WATER","water_cooler_count"),

    /**
     * 功率转换器
     */
    PCS("PCS","pcs_count"),

    /**
     * 能量管理系统
     */
    BMS("BMS","bms_count"),

    /**
     * 电池簇
     */
    CLUSTER("CLUSTER","bms_cluster_count"),

    /**
     * 电池组
     */
    STACK("STACK","bms_stack_per_cluster_count"),

    /**
     * 电池芯
     */
    CELL ("CELL","bms_temperature_per_stack_count"),

    /**
     * 电表
     */
    METER ("METER","ems_meter_count"),
    /**
     * DCDC
     */
    DCDC("DCDC", "dcdc_count"),

    /**
     * DCDC
     */
    STATES("STATES", "states_count");

    private final String code;

    private final String column;

    OffsetTypeEnum(String code, String column) {
        this.code = code;
        this.column = column;
    }

    public static OffsetTypeEnum getByCode(String code){
        for(OffsetTypeEnum offsetTypeEnum :OffsetTypeEnum.values()){
            if(code.contains(offsetTypeEnum.getCode())){
                return offsetTypeEnum;
            }
        }
        throw new IllegalArgumentException("OffsetTypeEnum code not found: " + code);
    }

}



