package com.wifochina.modules.data.influxdb;

import com.influxdb.client.*;
import com.wifochina.common.util.EmsUtil;
import com.wifochina.common.util.HashUtil;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * @since 4/6/2022 3:27 PM
 * <AUTHOR>
 */
@Component
public class InfluxClient implements InitializingBean, InfluxClientService {

    @Resource private InfluxConfig influxConfig;

    private WriteApiBlocking writeApi;

    private QueryApi queryApi;

    private String realtimeBucket;

    private String foreverBucket;

    private String meanBucket;

    private String demandBucket;

    private static final String TABLE_EMS = "ems100";

    private static final String TABLE_METER = "meter";

    private static final String TABLE_CELL = "ems100_cell";

    private static final String TABLE_DEMAND_1M_SLIDING = "demand_15m_sliding_1m";

    private static final String TABLE_DEMAND_15M_FIXED = "demand_15m_fixed";

    private static final String TABLE_DEMAND_30M_FIXED = "demand_30m_fixed";

    public static final String KEY_DEVICE = "deviceId";

    public static final String KEY_METER = "meterId";

    @Override
    public void afterPropertiesSet() {

        String url = influxConfig.getUrl();
        String token = influxConfig.getToken();
        String org = influxConfig.getOrg();
        this.realtimeBucket = influxConfig.getRealtimeBucket();
        this.foreverBucket = influxConfig.getForeverBucket();
        this.meanBucket = influxConfig.getMeanBucket();
        this.demandBucket = influxConfig.getDemandBucket();
        assert url != null;
        assert token != null;
        InfluxDBClient influxDbClient = InfluxDBClientFactory.create(url, token.toCharArray(), org);

        writeApi = influxDbClient.getWriteApiBlocking();

        queryApi = influxDbClient.getQueryApi();
    }

    @Override
    public String getBucketRealtime() {
        return realtimeBucket;
    }

    @Override
    public String getBucketForever() {
        return foreverBucket;
    }

    @Override
    public String getBucketMean() {
        return meanBucket;
    }

    @Override
    public String getBucketDemand() {
        return demandBucket;
    }

    @Override
    public String getTableEms() {
        return TABLE_EMS;
    }

    @Override
    public String getTableMeter() {
        return TABLE_METER;
    }

    @Override
    public String getTableDemand1mSliding() {
        return TABLE_DEMAND_1M_SLIDING;
    }

    @Override
    public String getTableDemand15mFixed() {
        return TABLE_DEMAND_15M_FIXED;
    }

    @Override
    public String getTableDemand30mFixed() {
        return TABLE_DEMAND_30M_FIXED;
    }

    @Override
    public String getEmsTable(String projectId) {
        return TABLE_EMS + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public String getMeterTable(String projectId) {
        return TABLE_METER + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public String getCellTable(String projectId) {
        return TABLE_CELL + "@" + HashUtil.elfHash(projectId);
    }

    @Override
    public QueryApi getQueryApi() {
        return this.queryApi;
    }

    @Override
    public WriteApiBlocking getWriteApi() {
        return this.writeApi;
    }

    @Override
    public String getDeviceKey() {
        return KEY_DEVICE;
    }

    @Override
    public String getMeterKey() {
        return KEY_METER;
    }

    @Override
    public String createMeterSql(List<String> ammeterIds) {
        return EmsUtil.createMeterSqlForProxyDb(ammeterIds);
    }
}
