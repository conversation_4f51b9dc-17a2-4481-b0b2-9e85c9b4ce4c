package com.wifochina;

import com.wifochina.modules.data.controller.EmsEventController;
import com.wifochina.modules.data.request.EmsStateChangeData;
import com.wifochina.modules.data.request.EmsStateChangeRequest;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class EmsEventTest {

    @Resource private EmsEventController emsEventController;

    @Test
    public void test() {

        // 创建 AllBitCode 实例
        EmsStateChangeData.AllBitCode emsBitCode =
                new EmsStateChangeData.AllBitCode(
                        70, Arrays.asList(1, 0, 1), Arrays.asList(0, 1, 0), Arrays.asList(0, 0, 1));
        EmsStateChangeData.AllBitCode bmsBitCode =
                new EmsStateChangeData.AllBitCode(
                        70, Arrays.asList(0, 1, 1), Arrays.asList(1, 0, 1), Arrays.asList(1, 1, 0));

        // 创建 NoAlarmBitCode 实例
        EmsStateChangeData.NoAlarmBitCode waterCoolerBitCode1 =
                new EmsStateChangeData.NoAlarmBitCode(
                        65535, Arrays.asList(0, 1, 0), Arrays.asList(1, 0, 0));
        EmsStateChangeData.NoAlarmBitCode waterCoolerBitCode2 =
                new EmsStateChangeData.NoAlarmBitCode(
                        2, Arrays.asList(1, 1, 1), Arrays.asList(0, 1, 1));

        // 构建完整的 EmsStateChangeData 实例
        EmsStateChangeData emsStateChangeData =
                new EmsStateChangeData(
                        emsBitCode,
                        bmsBitCode,
                        // bmscluster
                        List.of(
                                new EmsStateChangeData.AllBitCode(
                                        1,
                                        Arrays.asList(1, 1, 0),
                                        Arrays.asList(0, 0, 1),
                                        Arrays.asList(1, 1, 1)),
                                new EmsStateChangeData.AllBitCode(
                                        1,
                                        Arrays.asList(1, 1, 0),
                                        Arrays.asList(0, 0, 1),
                                        Arrays.asList(1, 1, 1))),
                        // pcs
                        List.of(
                                new EmsStateChangeData.AllBitCode(
                                        0,
                                        Arrays.asList(0, 1, 1),
                                        Arrays.asList(1, 1, 0),
                                        Arrays.asList(0, 0, 1))),
                        // dcdc
                        List.of(
                                new EmsStateChangeData.AllBitCode(
                                        37,
                                        Arrays.asList(1, 0, 1),
                                        Arrays.asList(1, 1, 0),
                                        Arrays.asList(0, 1, 1)),
                                new EmsStateChangeData.AllBitCode(
                                        37,
                                        Arrays.asList(1, 0, 1),
                                        Arrays.asList(1, 1, 0),
                                        Arrays.asList(0, 1, 1))),
                        // state
                        List.of(
                                new EmsStateChangeData.AllBitCode(
                                        36,
                                        Arrays.asList(0, 1, 0),
                                        Arrays.asList(1, 0, 1),
                                        Arrays.asList(1, 1, 1))),
                        // air
                        List.of(
                                new EmsStateChangeData.NoAlarmBitCode(
                                        2, Arrays.asList(1, 0, 1), Arrays.asList(0, 1, 0))),
                        // fire
                        List.of(
                                new EmsStateChangeData.NoAlarmBitCode(
                                        8, Arrays.asList(0, 1, 1), Arrays.asList(1, 0, 1))),
                        Arrays.asList(waterCoolerBitCode1, waterCoolerBitCode2));

        EmsStateChangeData emsLastChangeData = createOppositeEmsStateChangeData(emsStateChangeData);

        // 创建 EmsStateChangeRequest 实例
        EmsStateChangeRequest request =
                new EmsStateChangeRequest(
                        "4f537620d37d40e19dd25be5ca6ad941",
                        System.currentTimeMillis(),
                        "5f206dfafef5452fa16dba6d2f1ec6ca",
                        emsStateChangeData,
                        emsLastChangeData);

        // 打印请求对象以验证数据
        System.out.println(request);
        emsEventController.emsChange(request);
        System.out.println("test");
    }

    // 方法用于创建与给定 EmsStateChangeData 取反的新实例
    public EmsStateChangeData createOppositeEmsStateChangeData(EmsStateChangeData original) {
        EmsStateChangeData.AllBitCode oppositeEmsBitCode =
                new EmsStateChangeData.AllBitCode(
                        original.getEmsBitCodes().getDeviceTypeCode(),
                        invertBits(original.getEmsBitCodes().getStateCode()),
                        invertBits(original.getEmsBitCodes().getAlarmCode()),
                        invertBits(original.getEmsBitCodes().getFaultCode()));

        EmsStateChangeData.AllBitCode oppositeBmsBitCode =
                new EmsStateChangeData.AllBitCode(
                        original.getBmsBitCodes().getDeviceTypeCode(),
                        invertBits(original.getBmsBitCodes().getStateCode()),
                        invertBits(original.getBmsBitCodes().getAlarmCode()),
                        invertBits(original.getBmsBitCodes().getFaultCode()));

        List<EmsStateChangeData.AllBitCode> oppositeBmsCluster =
                original.getBmsClusterBitCodes().stream()
                        .map(this::invertAllBitCode)
                        .collect(Collectors.toList());

        List<EmsStateChangeData.AllBitCode> oppositePcs =
                original.getPcsBitCodes().stream()
                        .map(this::invertAllBitCode)
                        .collect(Collectors.toList());

        List<EmsStateChangeData.AllBitCode> oppositeDcdc =
                original.getDcdcBitCodes().stream()
                        .map(this::invertAllBitCode)
                        .collect(Collectors.toList());

        List<EmsStateChangeData.AllBitCode> oppositeState =
                original.getStateBitCodes().stream()
                        .map(this::invertAllBitCode)
                        .collect(Collectors.toList());

        List<EmsStateChangeData.NoAlarmBitCode> oppositeAir =
                original.getAirConditionerBitCodes().stream()
                        .map(this::invertNoAlarmBitCode)
                        .collect(Collectors.toList());

        List<EmsStateChangeData.NoAlarmBitCode> oppositeFire =
                original.getFirefightingBitCodes().stream()
                        .map(this::invertNoAlarmBitCode)
                        .collect(Collectors.toList());

        List<EmsStateChangeData.NoAlarmBitCode> oppositeWaterCoolers =
                original.getWaterCoolerBitCodes().stream()
                        .map(this::invertNoAlarmBitCode)
                        .collect(Collectors.toList());

        return new EmsStateChangeData(
                oppositeEmsBitCode,
                oppositeBmsBitCode,
                oppositeBmsCluster,
                oppositePcs,
                oppositeDcdc,
                oppositeState,
                oppositeAir,
                oppositeFire,
                oppositeWaterCoolers);
    }

    // 辅助方法，用于反转 AllBitCode 的状态码、报警码和故障码
    private EmsStateChangeData.AllBitCode invertAllBitCode(EmsStateChangeData.AllBitCode bitCode) {
        return new EmsStateChangeData.AllBitCode(
                bitCode.getDeviceTypeCode(),
                invertBits(bitCode.getStateCode()),
                invertBits(bitCode.getAlarmCode()),
                invertBits(bitCode.getFaultCode()));
    }

    // 辅助方法，用于反转 NoAlarmBitCode 的状态码和故障码
    private EmsStateChangeData.NoAlarmBitCode invertNoAlarmBitCode(
            EmsStateChangeData.NoAlarmBitCode bitCode) {
        return new EmsStateChangeData.NoAlarmBitCode(
                bitCode.getDeviceTypeCode(),
                invertBits(bitCode.getStateCode()),
                invertBits(bitCode.getFaultCode()));
    }

    // 反转单个位的辅助方法
    private List<Integer> invertBits(List<Integer> bits) {
        return bits.stream().map(i -> i ^ 1).collect(Collectors.toList());
    }
}
