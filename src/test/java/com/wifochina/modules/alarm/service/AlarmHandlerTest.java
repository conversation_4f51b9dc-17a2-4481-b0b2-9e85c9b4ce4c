package com.wifochina.modules.alarm.service;

import com.alibaba.fastjson.JSON;
import com.weihengtech.alert.client.AlertApiClient;
import com.weihengtech.alert.model.base.BaseAlert;
import com.weihengtech.alert.model.dto.AlertCreateDTO;
import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmLevelEnum;
import com.wifochina.common.constants.CommonConstants;
import com.wifochina.common.delay.DelayQueueMessage;
import com.wifochina.common.runner.DelayQueueInit;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.EventMessageEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AlarmHandler 单元测试
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@ExtendWith(MockitoExtension.class)
class AlarmHandlerTest {

    @Mock
    private AlarmConfigService alarmConfigService;

    @Mock
    private AlarmSwitchService alarmSwitchService;

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private AlertApiClient alertApiClient;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @InjectMocks
    private AlarmHandler alarmHandler;

    private EventMessageEntity eventMessage;
    private AlarmCacheDTO alarmCacheDTO;
    private AlarmConfigEntity alarmConfigEntity;
    private AlarmSwitchEntity alarmSwitchEntity;

    @BeforeEach
    void setUp() {
        // 设置测试属性值
        ReflectionTestUtils.setField(alarmHandler, "appName", "ems-data-analysis");
        ReflectionTestUtils.setField(alarmHandler, "monitor", "ems-monitor");

        // 初始化测试数据
        eventMessage = new EventMessageEntity();
        eventMessage.setProjectId("test-project-id");
        eventMessage.setEventKey("test-event-code");
        eventMessage.setEventType(EventLevelEnum.ALARM.getLevel());
        eventMessage.setEventOnOff("on");

        alarmConfigEntity = new AlarmConfigEntity();
        alarmConfigEntity.setAlarmContent(AlarmContentEnum.PROJECT_ALARM.getCode());
        alarmConfigEntity.setIsEnabled(true);
        alarmConfigEntity.setDowntimeThreshold("10");

        alarmSwitchEntity = new AlarmSwitchEntity();
        alarmSwitchEntity.setEventCode("test-event-code");
        alarmSwitchEntity.setProjectId("test-project-id");

        alarmCacheDTO = AlarmCacheDTO.builder()
                .alarmConfigList(List.of(alarmConfigEntity))
                .allEmsKernelAlarmEnabled(true)
                .allEmsSubDeviceAlarmEnabled(true)
                .allMeterAlarmEnabled(true)
                .alarmSwitchList(List.of(alarmSwitchEntity))
                .build();

        // Mock Redis 操作
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(true);
    }

    @Test
    void testCacheAlarmInfo_WhenRedisKeyExists_ShouldReturnCachedData() {
        // Given
        String projectId = "test-project-id";
        String cachedData = JSON.toJSONString(alarmCacheDTO);
        when(valueOperations.get(anyString())).thenReturn(cachedData);

        // When
        AlarmCacheDTO result = alarmHandler.cacheAlarmInfo(projectId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAlarmConfigList()).hasSize(1);
        assertThat(result.getAllEmsKernelAlarmEnabled()).isTrue();
        verify(stringRedisTemplate, never()).opsForValue();
    }

    @Test
    void testCacheAlarmInfo_WhenRedisKeyNotExists_ShouldFetchAndCache() {
        // Given
        String projectId = "test-project-id";
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);
        when(valueOperations.get(anyString())).thenReturn(null);
        when(alarmConfigService.getByProjectId(projectId)).thenReturn(List.of(alarmConfigEntity));
        when(alarmSwitchService.listAlarmSwitch(projectId)).thenReturn(alarmCacheDTO);

        // When
        AlarmCacheDTO result = alarmHandler.cacheAlarmInfo(projectId);

        // Then
        assertThat(result).isNotNull();
        verify(alarmConfigService).getByProjectId(projectId);
        verify(alarmSwitchService).listAlarmSwitch(projectId);
        verify(valueOperations).set(anyString(), anyString());
    }

    @Test
    void testCacheAlarmInfo_WhenNoAlarmConfig_ShouldReturnEmptyDTO() {
        // Given
        String projectId = "test-project-id";
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);
        when(valueOperations.get(anyString())).thenReturn(null);
        when(alarmConfigService.getByProjectId(projectId)).thenReturn(Collections.emptyList());

        // When
        AlarmCacheDTO result = alarmHandler.cacheAlarmInfo(projectId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAlarmConfigList()).isEmpty();
        verify(alarmSwitchService, never()).listAlarmSwitch(anyString());
    }

    @Test
    void testHandleAlarm_WhenAlarmConfigListEmpty_ShouldReturnEarly() {
        // Given
        AlarmCacheDTO emptyCache = AlarmCacheDTO.builder().alarmConfigList(Collections.emptyList()).build();
        when(alarmConfigService.getByProjectId(anyString())).thenReturn(Collections.emptyList());
        when(alarmSwitchService.listAlarmSwitch(anyString())).thenReturn(emptyCache);
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);

        // When
        alarmHandler.handleAlarm(true, "meter", eventMessage);

        // Then
        verify(alertApiClient, never()).sendAlert(any());
        verify(DelayQueueInit.DELAY_QUEUE, never()).offer(any());
    }

    @Test
    void testHandleAlarm_WhenConfigNotMatch_ShouldReturnEarly() {
        // Given
        alarmConfigEntity.setAlarmContent(AlarmContentEnum.PROJECT_FAULT.getCode());
        alarmConfigEntity.setIsEnabled(false);
        when(alarmConfigService.getByProjectId(anyString())).thenReturn(List.of(alarmConfigEntity));
        when(alarmSwitchService.listAlarmSwitch(anyString())).thenReturn(alarmCacheDTO);
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);

        // When
        alarmHandler.handleAlarm(true, "meter", eventMessage);

        // Then
        verify(alertApiClient, never()).sendAlert(any());
        verify(DelayQueueInit.DELAY_QUEUE, never()).offer(any());
    }

    @Test
    void testHandleAlarm_WhenSwitchNotMatch_ShouldReturnEarly() {
        // Given
        alarmCacheDTO.setAllMeterAlarmEnabled(false);
        alarmCacheDTO.setAlarmSwitchList(Collections.emptyList());
        when(alarmConfigService.getByProjectId(anyString())).thenReturn(List.of(alarmConfigEntity));
        when(alarmSwitchService.listAlarmSwitch(anyString())).thenReturn(alarmCacheDTO);
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);

        // When
        alarmHandler.handleAlarm(true, "meter", eventMessage);

        // Then
        verify(alertApiClient, never()).sendAlert(any());
        verify(DelayQueueInit.DELAY_QUEUE, never()).offer(any());
    }

    @Test
    void testHandleAlarm_WhenStopEvent_ShouldAddToDelayQueue() {
        // Given
        eventMessage.setEventKey(CommonConstants.EVENT_CODE_STOP1);
        alarmConfigEntity.setAlarmContent(AlarmContentEnum.DEVICE_STOP.getCode());
        alarmConfigEntity.setIsEnabled(true);
        when(alarmConfigService.getByProjectId(anyString())).thenReturn(List.of(alarmConfigEntity));
        when(alarmSwitchService.listAlarmSwitch(anyString())).thenReturn(alarmCacheDTO);
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);

        // Mock DelayQueue
        try (MockedStatic<DelayQueueInit> mockedStatic = mockStatic(DelayQueueInit.class)) {
            mockedStatic.when(() -> DelayQueueInit.DELAY_QUEUE.offer(any(DelayQueueMessage.class)))
                    .thenReturn(true);

            // When
            alarmHandler.handleAlarm(false, "ems", eventMessage);

            // Then
            verify(valueOperations).set(anyString(), eq(""), eq(30L), eq(TimeUnit.MINUTES));
            mockedStatic.verify(() -> DelayQueueInit.DELAY_QUEUE.offer(any(DelayQueueMessage.class)));
        }
    }

    @Test
    void testHandleAlarm_WhenRegularAlarm_ShouldSendAlert() {
        // Given
        when(alarmConfigService.getByProjectId(anyString())).thenReturn(List.of(alarmConfigEntity));
        when(alarmSwitchService.listAlarmSwitch(anyString())).thenReturn(alarmCacheDTO);
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);

        // When
        alarmHandler.handleAlarm(true, "meter", eventMessage);

        // Then
        verify(alertApiClient).sendAlert(any(AlertCreateDTO.class));
        verify(DelayQueueInit.DELAY_QUEUE, never()).offer(any());
    }

    @Test
    void testCheckAlarmSwitch_WhenMeterAndAllEnabled_ShouldReturnTrue() {
        // Given
        alarmCacheDTO.setAllMeterAlarmEnabled(true);

        // When
        boolean result = alarmHandler.checkAlarmSwitch(alarmCacheDTO, eventMessage, true, "meter");

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testCheckAlarmSwitch_WhenMeterAndNotAllEnabledButEventInList_ShouldReturnTrue() {
        // Given
        alarmCacheDTO.setAllMeterAlarmEnabled(false);
        alarmCacheDTO.setAlarmSwitchList(List.of(alarmSwitchEntity));

        // When
        boolean result = alarmHandler.checkAlarmSwitch(alarmCacheDTO, eventMessage, true, "meter");

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testCheckAlarmSwitch_WhenMeterAndNotAllEnabledAndEventNotInList_ShouldReturnFalse() {
        // Given
        alarmCacheDTO.setAllMeterAlarmEnabled(false);
        alarmSwitchEntity.setEventCode("different-event-code");
        alarmCacheDTO.setAlarmSwitchList(List.of(alarmSwitchEntity));

        // When
        boolean result = alarmHandler.checkAlarmSwitch(alarmCacheDTO, eventMessage, true, "meter");

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testCheckAlarmSwitch_WhenEmsKernelAndAllEnabled_ShouldReturnTrue() {
        // Given
        alarmCacheDTO.setAllEmsKernelAlarmEnabled(true);

        // When
        boolean result = alarmHandler.checkAlarmSwitch(alarmCacheDTO, eventMessage, false, CommonConstants.DEVICE_TYPE_EMS_KERNEL);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testCheckAlarmConfig_WhenFaultEventAndConfigEnabled_ShouldReturnTrue() {
        // Given
        List<AlarmConfigEntity> configList = List.of(alarmConfigEntity);
        alarmConfigEntity.setAlarmContent(AlarmContentEnum.PROJECT_FAULT.getCode());
        alarmConfigEntity.setIsEnabled(true);
        String eventType = EventLevelEnum.FAULT.getLevel();

        // When
        boolean result = alarmHandler.checkAlarmConfig(configList, eventType);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testCheckAlarmConfig_WhenAlarmEventAndConfigDisabled_ShouldReturnFalse() {
        // Given
        List<AlarmConfigEntity> configList = List.of(alarmConfigEntity);
        alarmConfigEntity.setAlarmContent(AlarmContentEnum.PROJECT_ALARM.getCode());
        alarmConfigEntity.setIsEnabled(false);
        String eventType = EventLevelEnum.ALARM.getLevel();

        // When
        boolean result = alarmHandler.checkAlarmConfig(configList, eventType);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testCheckAlarmConfig_WhenOtherEventType_ShouldReturnTrue() {
        // Given
        List<AlarmConfigEntity> configList = List.of(alarmConfigEntity);
        String eventType = "other-event-type";

        // When
        boolean result = alarmHandler.checkAlarmConfig(configList, eventType);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testIsStopEvent_WhenStopEvent1AndConfigEnabled_ShouldReturnTrue() {
        // Given
        eventMessage.setEventKey(CommonConstants.EVENT_CODE_STOP1);
        alarmConfigEntity.setAlarmContent(AlarmContentEnum.DEVICE_STOP.getCode());
        alarmConfigEntity.setIsEnabled(true);

        // When
        boolean result = alarmHandler.isStopEvent(alarmCacheDTO, eventMessage);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void testIsStopEvent_WhenStopEvent2ButConfigDisabled_ShouldReturnFalse() {
        // Given
        eventMessage.setEventKey(CommonConstants.EVENT_CODE_STOP2);
        alarmConfigEntity.setAlarmContent(AlarmContentEnum.DEVICE_STOP.getCode());
        alarmConfigEntity.setIsEnabled(false);

        // When
        boolean result = alarmHandler.isStopEvent(alarmCacheDTO, eventMessage);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testIsStopEvent_WhenNotStopEvent_ShouldReturnFalse() {
        // Given
        eventMessage.setEventKey("other-event-code");

        // When
        boolean result = alarmHandler.isStopEvent(alarmCacheDTO, eventMessage);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void testGetAlertCreate_WhenAlarmEvent_ShouldCreateCorrectAlert() {
        // Given
        eventMessage.setEventType(EventLevelEnum.ALARM.getLevel());

        // When
        AlertCreateDTO result = alarmHandler.getAlertCreate(eventMessage, EventLevelEnum.ALARM.getLevel());

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSource()).isEqualTo("ems-data-analysis");
        assertThat(result.getSubGroup()).isEqualTo("test-project-id");
        assertThat(result.getAlerts()).hasSize(1);
        
        BaseAlert alert = result.getAlerts().get(0);
        assertThat(alert.getStatus()).isEqualTo(BaseAlert.STATUS_FIRING);
        assertThat(alert.getLabels()).containsEntry(CommonConstants.LABLE_ALERT_NAME, AlarmContentEnum.PROJECT_ALARM.getName());
        assertThat(alert.getLabels()).containsEntry(CommonConstants.LABLE_LEVEL, AlarmLevelEnum.warning.name());
    }

    @Test
    void testGetAlertCreate_WhenFaultEvent_ShouldCreateCorrectAlert() {
        // Given
        eventMessage.setEventType(EventLevelEnum.FAULT.getLevel());
        eventMessage.setEventOnOff("off");

        // When
        AlertCreateDTO result = alarmHandler.getAlertCreate(eventMessage, EventLevelEnum.FAULT.getLevel());

        // Then
        assertThat(result).isNotNull();
        
        BaseAlert alert = result.getAlerts().get(0);
        assertThat(alert.getStatus()).isEqualTo(BaseAlert.STATUS_RESOLVE);
        assertThat(alert.getLabels()).containsEntry(CommonConstants.LABLE_ALERT_NAME, AlarmContentEnum.PROJECT_FAULT.getName());
        assertThat(alert.getLabels()).containsEntry(CommonConstants.LABLE_LEVEL, AlarmLevelEnum.emergency.name());
    }

    @Test
    void testGetBaseAlert_WhenEventOn_ShouldSetFiringStatus() {
        // Given
        eventMessage.setEventOnOff("on");

        // When
        BaseAlert result = alarmHandler.getBaseAlert(eventMessage, EventLevelEnum.ALARM.getLevel());

        // Then
        assertThat(result.getStatus()).isEqualTo(BaseAlert.STATUS_FIRING);
    }

    @Test
    void testGetBaseAlert_WhenEventOff_ShouldSetResolveStatus() {
        // Given
        eventMessage.setEventOnOff("off");

        // When
        BaseAlert result = alarmHandler.getBaseAlert(eventMessage, EventLevelEnum.ALARM.getLevel());

        // Then
        assertThat(result.getStatus()).isEqualTo(BaseAlert.STATUS_RESOLVE);
    }
}