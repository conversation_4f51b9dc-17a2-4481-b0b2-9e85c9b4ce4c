package com.wifochina.modules.alarm.service;

import com.alibaba.fastjson.JSON;
import com.weihengtech.alert.client.AlertApiClient;
import com.weihengtech.alert.model.base.BaseAlert;
import com.weihengtech.alert.model.dto.AlertCreateDTO;
import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmLevelEnum;
import com.wifochina.common.constants.CommonConstants;
import com.wifochina.common.delay.DelayQueueMessage;
import com.wifochina.common.runner.DelayQueueInit;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.service.AlarmSwitchService;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 告警配置 服务类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Component
public class AlarmHandler {

    @Value("${alert.api.app-name}")
    private String appName;

    @Value("${alert.api.monitor}")
    private String monitor;

    @Resource private AlarmConfigService alarmConfigService;
    @Resource private AlarmSwitchService alarmSwitchService;
    @Resource private StringRedisTemplate stringRedisTemplate;
    @Resource private AlertApiClient alertApiClient;

    /**
     * 缓存告警配置
     *
     * @param projectId
     * @return
     */
    public AlarmCacheDTO cacheAlarmInfo(String projectId) {
        if (!stringRedisTemplate.hasKey(CommonConstants.buildAlarmSwitchRedisKey(projectId))) {
            String cacheStr = stringRedisTemplate.opsForValue().get(projectId);
            if (StringUtils.isBlank(cacheStr)) {
                return AlarmCacheDTO.builder().build();
            }
            return JSON.parseObject(cacheStr, AlarmCacheDTO.class);
        }
        AlarmCacheDTO res = AlarmCacheDTO.builder().build();
        List<AlarmConfigEntity> alarmConfigList = alarmConfigService.getByProjectId(projectId);
        if (CollectionUtils.isEmpty(alarmConfigList)) {
            return res;
        }
        res.setAlarmConfigList(alarmConfigList);
        AlarmCacheDTO cacheItem = alarmSwitchService.listAlarmSwitch(projectId);
        res.setAllEmsKernelAlarmEnabled(cacheItem.getAllEmsKernelAlarmEnabled());
        res.setAllEmsSubDeviceAlarmEnabled(cacheItem.getAllEmsSubDeviceAlarmEnabled());
        res.setAllMeterAlarmEnabled(cacheItem.getAllMeterAlarmEnabled());
        res.setAlarmSwitchList(cacheItem.getAlarmSwitchList());
        stringRedisTemplate
                .opsForValue()
                .set(
                        CommonConstants.buildAlarmSwitchRedisKey(projectId),
                        JSON.toJSONString(cacheItem));
        return cacheItem;
    }

    /**
     * 根据配置发送告警通知
     *
     * @param isMeter
     * @param deviceType
     * @param eventMessage
     */
    public void handleAlarm(boolean isMeter, String deviceType, EventMessageEntity eventMessage) {
        // 获取告警配置缓存
        AlarmCacheDTO cacheItem = cacheAlarmInfo(eventMessage.getProjectId());
        if (CollectionUtils.isEmpty(cacheItem.getAlarmConfigList())) {
            return;
        }
        List<AlarmConfigEntity> alarmConfigList = cacheItem.getAlarmConfigList();
        String eventType = eventMessage.getEventType();
        // 校验告警配置
        boolean configMatch = checkAlarmConfig(alarmConfigList, eventType);
        if (!configMatch) {
            return;
        }
        // 校验事件告警开关
        boolean switchMatch = checkAlarmSwitch(cacheItem, eventMessage, isMeter, deviceType);
        if (!switchMatch) {
            return;
        }
        boolean isStopEvent = isStopEvent(cacheItem, eventMessage);
        if (isStopEvent) {
            // 走停机告警逻辑
            String redisKey =
                    CommonConstants.buildStopRedisKey(
                            eventMessage.getProjectId(), eventMessage.getEventKey());
            long time =
                    cacheItem.getAlarmConfigList().stream()
                            .filter(
                                    i ->
                                            i.getAlarmContent()
                                                    == AlarmContentEnum.DEVICE_STOP.getCode())
                            .map(AlarmConfigEntity::getDowntimeThreshold)
                            .map(Long::valueOf)
                            .findFirst()
                            .orElse(0L);
            stringRedisTemplate.opsForValue().set(redisKey, "", 3 * time, TimeUnit.MINUTES);
            DelayQueueInit.DELAY_QUEUE.offer(
                    DelayQueueMessage.builder()
                            .eventRedisKey(redisKey)
                            .eventMessage(eventMessage)
                            .delayTime(System.currentTimeMillis() + time * 60 * 1000)
                            .setTime(time)
                            .turns(1)
                            .build());
        } else {
            // 走故障、告警逻辑
            AlertCreateDTO param = getAlertCreate(eventMessage, eventType);
            alertApiClient.sendAlert(param);
        }
    }

    private boolean isStopEvent(AlarmCacheDTO cacheItem, EventMessageEntity eventMessage) {
        String eventCode = eventMessage.getEventKey();
        if (CommonConstants.EVENT_CODE_STOP1.equals(eventCode)
                || CommonConstants.EVENT_CODE_STOP2.equals(eventCode)) {
            return cacheItem.getAlarmConfigList().stream()
                    .anyMatch(
                            item ->
                                    item.getAlarmContent() == AlarmContentEnum.DEVICE_STOP.getCode()
                                            && item.getIsEnabled());
        }
        return false;
    }

    /**
     * 校验事件是否开启告警
     *
     * @param cacheItem
     * @param eventMessage
     * @param isMeter
     * @param deviceType
     * @return
     */
    private boolean checkAlarmSwitch(
            AlarmCacheDTO cacheItem,
            EventMessageEntity eventMessage,
            boolean isMeter,
            String deviceType) {
        // 匹配是否开启告警通知
        if (isMeter) {
            if (!cacheItem.getAllMeterAlarmEnabled()) {
                List<AlarmSwitchEntity> alarmSwitchList = cacheItem.getAlarmSwitchList();
                return alarmSwitchList.stream()
                        .anyMatch(item -> item.getEventCode().equals(eventMessage.getEventKey()));
            }
        } else {
            if (CommonConstants.DEVICE_TYPE_EMS_KERNEL.equals(deviceType)) {
                if (!cacheItem.getAllEmsKernelAlarmEnabled()) {
                    List<AlarmSwitchEntity> alarmSwitchList = cacheItem.getAlarmSwitchList();
                    return alarmSwitchList.stream()
                            .anyMatch(
                                    item -> item.getEventCode().equals(eventMessage.getEventKey()));
                }
            } else if (!cacheItem.getAllEmsSubDeviceAlarmEnabled()) {
                List<AlarmSwitchEntity> alarmSwitchList = cacheItem.getAlarmSwitchList();
                return alarmSwitchList.stream()
                        .anyMatch(item -> item.getEventCode().equals(eventMessage.getEventKey()));
            }
        }
        return true;
    }

    /**
     * 校验告警配置是否匹配
     *
     * @param alarmConfigList
     * @param eventType
     * @return
     */
    private boolean checkAlarmConfig(List<AlarmConfigEntity> alarmConfigList, String eventType) {
        if (EventLevelEnum.FAULT.getLevel().equals(eventType)) {
            return alarmConfigList.stream()
                    .anyMatch(
                            item ->
                                    item.getAlarmContent()
                                                    == AlarmContentEnum.PROJECT_FAULT.getCode()
                                            && item.getIsEnabled());
        } else if (EventLevelEnum.ALARM.getLevel().equals(eventType)) {
            return alarmConfigList.stream()
                    .anyMatch(
                            item ->
                                    item.getAlarmContent()
                                                    == AlarmContentEnum.PROJECT_ALARM.getCode()
                                            && item.getIsEnabled());
        }
        return true;
    }

    @NotNull
    private AlertCreateDTO getAlertCreate(EventMessageEntity eventMessage, String eventType) {
        AlertCreateDTO param = new AlertCreateDTO();
        param.setSource(appName);
        param.setSubGroup(eventMessage.getProjectId());
        BaseAlert baseAlert = getBaseAlert(eventMessage, eventType);
        param.setAlerts(Collections.singletonList(baseAlert));
        return param;
    }

    @NotNull
    private BaseAlert getBaseAlert(EventMessageEntity eventMessage, String eventType) {
        BaseAlert baseAlert = new BaseAlert();
        String alertName;
        String level;
        if (EventLevelEnum.ALARM.getLevel().equals(eventType)) {
            alertName = AlarmContentEnum.PROJECT_ALARM.getName();
            level = AlarmLevelEnum.warning.name();
        } else {
            alertName = AlarmContentEnum.PROJECT_FAULT.getName();
            level = AlarmLevelEnum.emergency.name();
        }
        baseAlert.setStatus(
                "on".equals(eventMessage.getEventOnOff())
                        ? BaseAlert.STATUS_FIRING
                        : BaseAlert.STATUS_RESOLVE);
        baseAlert.setLabels(
                Map.of(
                        CommonConstants.LABLE_ALERT_NAME, alertName,
                        CommonConstants.LABLE_MONITOR, monitor,
                        CommonConstants.LABLE_LEVEL, level));
        return baseAlert;
    }
}
