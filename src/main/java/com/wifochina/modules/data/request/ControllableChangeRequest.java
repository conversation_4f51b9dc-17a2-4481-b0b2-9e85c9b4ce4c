package com.wifochina.modules.data.request;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * StateChangeRequest
 *
 * @since 7/15/2022 1:05 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ControllableChangeRequest {
    @ApiModelProperty(value = "设备id", required = true)
    private String deviceId;

    @ApiModelProperty(value = "项目id", required = true)
    private String projectId;

    @ApiModelProperty(value = "设备类型码", required = true)
    private String deviceTypeCode;

    @ApiModelProperty(value = "当前事件状态", required = true)
    private Integer[] currentStateCode;

    @ApiModelProperty(value = "上次事件状态", required = true)
    private Integer[] lastStateCode;

    @ApiModelProperty(value = "当前告警状态", required = true)
    private Integer[] currentAlarmCode;

    @ApiModelProperty(value = "上次告警状态", required = true)
    private Integer[] lastAlarmCode;

    @ApiModelProperty(value = "当前故障状态", required = true)
    private Integer[] currentFaultCode;

    @ApiModelProperty(value = "上周故障状态", required = true)
    private Integer[] lastFaultCode;

    @ApiModelProperty(value = "采集时间", required = true)
    private long time;
}
