package com.wifochina.modules.data.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @description: EmsDataRequest
 * @date: 7/14/2022 2:39 PM
 * @author: jacob.sun
 * @version: 1.0
 */
@Data
public class EmsDataRequest {
    @ApiModelProperty(value = "项目id", required = true)
    private String projectId;

    @ApiModelProperty(value = "EMS503接口数据", required = true)
    private Map<String, int[]> metric;

    @ApiModelProperty(value = "采集时间", required = true)
    private long time;
}
