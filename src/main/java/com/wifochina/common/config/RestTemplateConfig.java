package com.wifochina.common.config;

import com.wifochina.common.util.EmsConstants;

import okhttp3.OkHttpClient;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.validation.constraints.NotNull;

/**
 * RestTemplateConfig
 *
 * @since 3/29/2022 2:15 PM
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
public class RestTemplateConfig {

    @Value("${influx.token}")
    private String token;

    @Bean
    public RestTemplate restTemplate() {
        OkHttpClient okHttpClient =
                new OkHttpClient.Builder()
                        .connectTimeout(5, TimeUnit.SECONDS)
                        .readTimeout(3, TimeUnit.MINUTES)
                        .retryOnConnectionFailure(false)
                        .connectionPool(new okhttp3.ConnectionPool(10, 5, TimeUnit.MINUTES))
                        .build();

        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
        RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.setInterceptors(List.of(new RequestInterceptor()));
        return restTemplate;
    }

    class RequestInterceptor implements ClientHttpRequestInterceptor {
        @Override
        public ClientHttpResponse intercept(
                HttpRequest request, @NotNull byte[] body, ClientHttpRequestExecution execution)
                throws IOException {
            request.getHeaders().set(EmsConstants.AUTHORIZATION_HEADER, token);
            request.getHeaders().set(HttpHeaders.CONTENT_TYPE, "application/vnd.flux");
            return execution.execute(request, body);
        }
    }
}
