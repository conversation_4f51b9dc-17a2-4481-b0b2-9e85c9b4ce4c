package com.wifochina.common.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Objects;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.modules.data.entity.TypeCodeIndexEnum;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.GroupAmmeterEntity;
import com.wifochina.modules.group.entity.GroupDeviceEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.GroupAmmeterService;
import com.wifochina.modules.group.service.GroupDeviceService;
import com.wifochina.modules.group.service.GroupService;

import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-03-06 2:40 PM
 */
public class EmsUtil {

    public static int getPeriod(Long start, Long end, int interval) {
        MyTimeUtil.checkTime(start, end);
        // 相差的天数
        int periodTime = (int) ((end - start) / 86400);
        // 5天之类 间隔为15分钟，系数为1 ；6-10天，间隔为30分钟，系数为2;以此类推
        return (periodTime / 2 + 1) * interval;
    }

    public static List<String> getMeterIdsFromGroupAmmeterEntity(
            List<GroupAmmeterEntity> list, String type, AmmeterService ammeterService) {
        List<String> ammeterIdsTemp =
                list.stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .map(String::valueOf)
                        .collect(Collectors.toList());
        return ammeterService
                .list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .in(AmmeterEntity::getId, ammeterIdsTemp)
                                .eq(AmmeterEntity::getType, type))
                .stream()
                .map(AmmeterEntity::getId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    public static List<String> getGridMeterIdsByGroupId(
            String groupId,
            AmmeterService ammeterService,
            GroupAmmeterService groupAmmeterService) {
        List<String> groupMeterIds =
                groupAmmeterService
                        .lambdaQuery()
                        .eq(GroupAmmeterEntity::getGroupId, groupId)
                        .list()
                        .stream()
                        .map(GroupAmmeterEntity::getAmmeterId)
                        .collect(Collectors.toList());
        return ammeterService
                .lambdaQuery()
                .eq(AmmeterEntity::getType, 2)
                .in(AmmeterEntity::getId, groupMeterIds)
                .list()
                .stream()
                .map(AmmeterEntity::getId)
                .collect(Collectors.toList());
    }

    public static List<String> getDeviceIdsByGroupId(
            String groupId, GroupDeviceService groupDeviceService) {
        return groupDeviceService
                .lambdaQuery()
                .eq(GroupDeviceEntity::getGroupId, groupId)
                .list()
                .stream()
                .map(GroupDeviceEntity::getDeviceId)
                .collect(Collectors.toList());
    }

    public static List<String> getMeterIds(
            String projectId,
            String groupId,
            String itemId,
            Integer type,
            GroupAmmeterService groupAmmeterService,
            GroupService groupService,
            AmmeterService ammeterService) {
        List<String> ammeterIds = new ArrayList<>();
        // 根据请求中的itemId和groupId获取有效的GroupAmmeterEntity列表
        List<GroupAmmeterEntity> groupAmmeterEntities;
        if (Objects.equal(itemId, EmsConstants.ALL) || !StringUtils.hasLength(itemId)) {
            // 获取项目下的所有GroupEntity
            List<GroupEntity> groupEntities =
                    groupService.lambdaQuery().eq(GroupEntity::getProjectId, projectId).list();
            if (Objects.equal(groupId, EmsConstants.ALL) || !StringUtils.hasLength(groupId)) {
                // 如果groupId也为ALL或为空，则根据所有group的id查询GroupAmmeterEntity
                groupAmmeterEntities =
                        groupAmmeterService
                                .lambdaQuery()
                                .in(
                                        GroupAmmeterEntity::getGroupId,
                                        groupEntities.stream()
                                                .map(GroupEntity::getId)
                                                .collect(Collectors.toList()))
                                .list();
            } else {
                // 否则根据指定groupId查询GroupAmmeterEntity
                groupAmmeterEntities =
                        groupAmmeterService
                                .lambdaQuery()
                                .eq(GroupAmmeterEntity::getGroupId, groupId)
                                .list();
            }
        } else {
            // 如果itemId不为ALL且非空，则直接添加到ammeterIds中
            ammeterIds.add(itemId);
            // 此处假设当itemId不为ALL时，无需查询数据库获取GroupAmmeterEntity
            groupAmmeterEntities = Collections.emptyList();
        }

        // 对查询结果进行过滤，只保留符合type要求的AmmeterEntity
        if (!groupAmmeterEntities.isEmpty()) {
            List<String> ammeterIdsTemp =
                    groupAmmeterEntities.stream()
                            .map(GroupAmmeterEntity::getAmmeterId)
                            .filter(id -> ammeterService.getById(id).getType().equals(type))
                            .distinct()
                            .collect(Collectors.toList());
            ammeterIds.addAll(ammeterIdsTemp);
        }
        return ammeterIds;
    }

    public static String createDeviceSqlForInfluxDb(List<String> deviceIds) {
        StringBuilder deviceIdSql = new StringBuilder("(");
        for (int i = 0; i < deviceIds.size(); i++) {
            if (i == 0) {
                deviceIdSql.append("r.deviceId ==\"").append(deviceIds.get(i)).append("\"");
            } else {
                deviceIdSql.append(" or r.deviceId == \"").append(deviceIds.get(i)).append("\"");
            }
        }
        deviceIdSql.append(")");
        return deviceIdSql.toString();
    }

    public static String createDeviceSqlForLindormDb(List<String> deviceIds) {
        StringBuilder deviceIdSql = new StringBuilder("and (");
        for (int i = 0; i < deviceIds.size(); i++) {
            if (i == 0) {
                deviceIdSql.append("deviceId ='").append(deviceIds.get(i)).append("'");
            } else {
                deviceIdSql.append(" or deviceId = '").append(deviceIds.get(i)).append("'");
            }
        }
        deviceIdSql.append(")");
        return deviceIdSql.toString();
    }

    public static String createMeterSqlForLindormDb(List<String> ammeterIds) {
        if (CollectionUtils.isEmpty(ammeterIds)) {
            return "";
        }
        StringBuilder ammeterIdSql = new StringBuilder("and (");
        for (int i = 0; i < ammeterIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("meterId = '").append(ammeterIds.get(i)).append("'");
            } else {
                ammeterIdSql.append(" or meterId = '").append(ammeterIds.get(i)).append("'");
            }
        }
        ammeterIdSql.append(")");
        return ammeterIdSql.toString();
    }

    public static String createGroupSqlForLindormDb(List<String> groupIds) {
        StringBuilder ammeterIdSql = new StringBuilder("and (");
        for (int i = 0; i < groupIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("groupId = '").append(groupIds.get(i)).append("'");
            } else {
                ammeterIdSql.append(" or groupId = '").append(groupIds.get(i)).append("'");
            }
        }
        ammeterIdSql.append(")");
        return ammeterIdSql.toString();
    }

    public static String createMeterSqlForInfluxDb(List<String> ammeterIds) {
        StringBuilder ammeterIdSql = new StringBuilder("(");
        for (int i = 0; i < ammeterIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("r.ammeterId ==\"").append(ammeterIds.get(i)).append("\"");
            } else {
                ammeterIdSql.append(" or r.ammeterId == \"").append(ammeterIds.get(i)).append("\"");
            }
        }
        ammeterIdSql.append(")");
        return ammeterIdSql.toString();
    }

    public static String createMeterSqlForProxyDb(List<String> ammeterIds) {
        StringBuilder ammeterIdSql = new StringBuilder("(");
        for (int i = 0; i < ammeterIds.size(); i++) {
            if (i == 0) {
                ammeterIdSql.append("r.meterId ==\"").append(ammeterIds.get(i)).append("\"");
            } else {
                ammeterIdSql.append(" or r.meterId == \"").append(ammeterIds.get(i)).append("\"");
            }
        }
        ammeterIdSql.append(")");
        return ammeterIdSql.toString();
    }

    public static int getTypeCode(String column, Integer index) {
        int typeCode = 0;
        if (column.contains(TypeCodeIndexEnum.PCS.getCode())) {
            // 2代表psc
            typeCode = TypeCodeIndexEnum.PCS.getIndex();
        } else if (column.contains(TypeCodeIndexEnum.BMS.getCode())) {
            // 4代表bms
            typeCode = TypeCodeIndexEnum.BMS.getIndex();
        } else if (column.contains(TypeCodeIndexEnum.AIR.getCode())) {
            // 6代表air
            typeCode = TypeCodeIndexEnum.AIR.getIndex();
        } else if (column.contains(TypeCodeIndexEnum.FIRE.getCode())) {
            // 8代表fire
            typeCode = TypeCodeIndexEnum.FIRE.getIndex();
        } else if (column.contains(TypeCodeIndexEnum.WATER_COOLER.getCode())) {
            // 15代表water_cooler
            typeCode = TypeCodeIndexEnum.WATER_COOLER.getIndex();
        } else if (column.contains(TypeCodeIndexEnum.DCDC.getCode())) {
            // 15代表water_cooler
            typeCode = TypeCodeIndexEnum.DCDC.getIndex();
        }
        if (column.contains(TypeCodeIndexEnum.STATES.getCode())) {
            typeCode = 21100 + index * 50;
        }
        return typeCode;
    }

    public static Double getPowerFactor(Double activePowerValue, Double reactivePowerValue) {
        Double apparentPower =
                Math.sqrt((Math.pow(activePowerValue, 2) + Math.pow(reactivePowerValue, 2)));
        return (Math.abs(activePowerValue / apparentPower));
    }

    public static String replaceTime(Long start, Long end, String queryString) {
        return queryString
                .replace("{start}", String.valueOf(start * 1000))
                .replace("{end}", String.valueOf(end * 1000));
    }

    public static String queryReplaceTime(Long start, Long end, String queryString) {
        LocalDateTime startDate = LocalDateTime.ofEpochSecond(start, 0, ZoneOffset.UTC);
        LocalDateTime endDate = LocalDateTime.ofEpochSecond(end - 240, 0, ZoneOffset.UTC);
        // 开始时间不晚于结束时间，否则返回时间错误异常
        ServiceAssert.isTrue(!endDate.isBefore(startDate), ErrorResultCode.TIME_OVERLAPPED.value());
        DateTimeFormatter stdUtcFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        queryString =
                queryString
                        .replace("{start}", stdUtcFormat.format(startDate))
                        .replace("{end}", stdUtcFormat.format(endDate));
        return queryString;
    }
}
