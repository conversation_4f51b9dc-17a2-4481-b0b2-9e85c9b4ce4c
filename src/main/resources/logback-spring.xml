<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志保存目录 -->
    <property name="LOG_PATH" value="/opt/log"/>

    <!-- 配置日志输出格式，调用方式：${LOG_PATTERN} -->
    <property name="LOG_PATTERN"
              value="%cyan(%d{yyyy-MM-dd HH:mm:ss,CTT}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{10}) - %msg%n"/>

    <!-- 配置系统日志输出 -->
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN}</pattern>
        </layout>
    </appender>

    <!-- 指定项目中某个类的日志输出到指定文件-->
    <appender name="OperationInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印info日志 -->
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <append>true</append>
        <encoder>
            <pattern>>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/operation.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
    </appender>

    <!--根据日志级别分离日志，分别输出到不同的文件-->
    <!-- info日志配置 -->
    <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印info日志 -->
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <append>true</append>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/logback.info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
    </appender>

    <!-- warn日志配置 -->
    <appender name="fileWarnLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印警告日志 -->
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
            <level>WARN</level>
        </filter>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/logback.warn.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- error日志配置-->

    <appender name="fileErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 只打印错误日志 -->
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/logback.error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>5</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 根据设置日志级别，屏蔽相关的日志：以下包及子包的日志会屏蔽掉-->
    <logger name="org.springframework" level="warn"/>
    <logger name="com.alibaba.druid" level="warn"/>
    <logger name="org.apache" level="warn"/>
    <logger name="springfox.documentation" level="warn"/>
    <logger name="com.baomidou.mybatisplus" level="error"/>
    <logger name="com.aliyun.lindorm.tsdb.client.shaded.com.squareup.okhttp3" level="error"/>


    <!-- 项目中info级别的日志会输出到以下日志对象中 -->
    <root level="INFO">
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="fileInfoLog"/>
        <appender-ref ref="fileWarnLog"/>
        <appender-ref ref="fileErrorLog"/>
        <!-- <appender-ref ref="recInfoLog"  /> -->
    </root>
</configuration>
