package com.wifochina.common.exception;

/**
 * 平台服务层异常，主要是在业务数据或者状态异常时使用
 * <AUTHOR>
 */
public class ServiceException extends RuntimeException{

	private static final long serialVersionUID = 1L;

	private String code;

    public ServiceException(String code, String message, Throwable cause) {
        super(message,cause);
        this.code = code;
    }

    public ServiceException(String code, String message) {
    	super(message);
        this.code = code;
    }
    
    public ServiceException(String code) {
        this.code = code;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
