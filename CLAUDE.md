# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Energy Management System (EMS) data analysis module built with Spring Boot. The system handles real-time data collection, processing, and analysis for energy monitoring devices including meters, controllers, and other IoT devices.

## Technology Stack

- **Java 11** with Spring Boot 2.1.4
- **MyBatis Plus** for database operations
- **InfluxDB** for time-series data storage (real-time, historical, and demand data)
- **MySQL** for relational data storage
- **Redis** for caching and message queuing
- **Maven** for build management
- **Docker** for containerization

## Key Dependencies

- InfluxDB Java Client (5.0.0) for time-series database operations
- MyBatis Plus (3.3.2) for ORM
- Knife4j (2.0.2) for API documentation
- FastJSON (1.2.45) for JSON processing
- Alert API Client for external alerting integration

## Build & Run Commands

```bash
# Build the project
mvn clean package

# Run the application
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Docker build
docker build -t ems-data-analysis .

# Test the application
mvn test
```

## Application Configuration

The application runs on port 8809 with context path `/api`. It uses profile-based configuration:

- `application-cloud.yml` - Cloud environment (default)
- `application-dev.yml` - Development environment
- `application-pro.yml` - Production environment
- `application-test.yml` - Test environment

## Architecture Overview

### Core Modules

1. **Data Module** (`modules.data`)
   - Real-time data collection from energy devices
   - Time-series data storage in InfluxDB
   - State change event processing
   - Controllers: DataController, OnlineDataController, ControllableDataController

2. **Alarm Module** (`modules.alarm`)
   - Alarm configuration and processing
   - Alert integration with external alert system
   - Event-based alarm triggering

3. **Event Module** (`modules.event`)
   - Event code management
   - Event message processing
   - Alarm switch configuration

4. **Group Module** (`modules.group`)
   - Device and meter grouping
   - Entity management for devices, meters, controllers
   - Hierarchical organization of energy assets

5. **Strategy Module** (`modules.strategy`)
   - Energy management strategies
   - Automated control logic

### Key Components

- **InfluxClient**: Handles all InfluxDB operations for time-series data
- **AlarmHandler**: Processes alarm events and integrates with external alert API
- **EventService**: Manages system events and state changes
- **Delay Queue**: Redis-based delayed message processing for alarms

### Data Flow

1. Device data → InfluxDB (real-time bucket)
2. State changes → Event processing → Alarm handling
3. Historical data aggregation → Permanent storage buckets
4. External alerts → Alert API integration

## Database Schema

### InfluxDB Buckets
- `realtime`: Real-time device data
- `forever`: Permanent historical data
- `mean`: Aggregated mean values
- `demand`: Demand analysis data

### MySQL Tables
- Device management (devices, meters, controllers)
- Group hierarchies
- Alarm configurations
- Event codes and messages
- User management

## Development Notes

- All timestamps are handled in UTC timezone
- The application uses Knife4j for API documentation (available at `/api/doc.html`)
- MyBatis Plus is configured with camelCase mapping
- Redis is used for caching and delayed message processing
- External alert integration uses AlertApiClient from weihengtech.alert

## Testing

The project includes JUnit 5 tests. Run tests with:
```bash
mvn test
```

## Environment Variables

Key environment variables:
- `PROFILES_ACTIVE`: Spring profile (default: cloud)
- `TSDB_URL`: InfluxDB server URL
- `TSDB_TOKEN`: InfluxDB authentication token
- `BUCKET_REALTIME`: Real-time data bucket name
- `BUCKET_FOREVER`: Permanent data bucket name
- `BUCKET_MEAN`: Mean values bucket name
- `BUCKET_DEMAND`: Demand data bucket name