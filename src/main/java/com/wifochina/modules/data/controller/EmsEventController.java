package com.wifochina.modules.data.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.data.entity.OffsetTypeEnum;
import com.wifochina.modules.data.listener.StateChange;
import com.wifochina.modules.data.request.EmsStateChangeData;
import com.wifochina.modules.data.request.EmsStateChangeRequest;
import com.wifochina.modules.data.service.EventService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.DeviceService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

@RestController
@Api(tags = "data收集")
@RequestMapping("/data")
@Slf4j
public class EmsEventController {

    @Resource private DeviceService deviceService;

    @Resource private EventService eventService;

    @Resource private HideEventCodeService hideEventCodeService;

    /** 判断可能事件发生(事件状态发生变化) data为空则未采集到数据 */
    @PostMapping("/emsChange")
    @ApiOperation("ems新版事件变化")
    public Result<T> emsChange(@RequestBody EmsStateChangeRequest emsStateChangeRequest) {
        emsStateChangeRequest.setTime(
                String.valueOf(emsStateChangeRequest.getTime()).length() == 13
                        ? emsStateChangeRequest.getTime()
                        : emsStateChangeRequest.getTime() * 1000);
        // 判断可能事件发生(事件状态发生变化) data为空则未采集到数据
        String deviceId = emsStateChangeRequest.getDeviceId();
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        if (deviceEntity == null) {
            return Result.success();
        }
        List<String> hideEventCodeList =
                hideEventCodeService.getEmsHideEventCode(deviceEntity.getProjectId());
        checkEmsSystemStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);
        checkBmsStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);
        checkCellStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);
        checkPcsStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);
        checkDcdcStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);
        checkStateEquipStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);
        checkAirConditionStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);
        checkFirefightingStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);
        checkWaterCoolerStateChange(hideEventCodeList, emsStateChangeRequest, deviceEntity);

        return Result.success();
    }

    public void checkEmsSystemStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        EmsStateChangeData.AllBitCode emsBitCodes =
                emsStateChangeRequest.getCurrentData().getEmsBitCodes();
        EmsStateChangeData.AllBitCode lastEmsBitCode =
                emsStateChangeRequest.getLastData().getEmsBitCodes();

        if (!Objects.equals(emsBitCodes, lastEmsBitCode)) {
            processBitChanges(
                    hideEventCodeList,
                    emsBitCodes.getStateCode(),
                    lastEmsBitCode.getStateCode(),
                    emsBitCodes.getDeviceTypeCode(),
                    deviceEntity,
                    emsStateChangeRequest.getTime(),
                    "EMS",
                    "system_state_bit_");
            processBitChanges(
                    hideEventCodeList,
                    emsBitCodes.getAlarmCode(),
                    lastEmsBitCode.getAlarmCode(),
                    emsBitCodes.getDeviceTypeCode(),
                    deviceEntity,
                    emsStateChangeRequest.getTime(),
                    "EMS",
                    "system_alarm_bit_");
            processBitChanges(
                    hideEventCodeList,
                    emsBitCodes.getFaultCode(),
                    lastEmsBitCode.getFaultCode(),
                    emsBitCodes.getDeviceTypeCode(),
                    deviceEntity,
                    emsStateChangeRequest.getTime(),
                    "EMS",
                    "system_fault_bit_");
        }
    }

    public void checkBmsStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        EmsStateChangeData.AllBitCode bmsBitCodes =
                emsStateChangeRequest.getCurrentData().getBmsBitCodes();
        EmsStateChangeData.AllBitCode lastBmsBitCode =
                emsStateChangeRequest.getLastData().getBmsBitCodes();

        if (!Objects.equals(bmsBitCodes, lastBmsBitCode)) {
            processBitChanges(
                    hideEventCodeList,
                    bmsBitCodes.getStateCode(),
                    lastBmsBitCode.getStateCode(),
                    bmsBitCodes.getDeviceTypeCode(),
                    deviceEntity,
                    emsStateChangeRequest.getTime(),
                    OffsetTypeEnum.BMS.getCode(),
                    "bms_state_bit_");
            processBitChanges(
                    hideEventCodeList,
                    bmsBitCodes.getAlarmCode(),
                    lastBmsBitCode.getAlarmCode(),
                    bmsBitCodes.getDeviceTypeCode(),
                    deviceEntity,
                    emsStateChangeRequest.getTime(),
                    OffsetTypeEnum.BMS.getCode(),
                    "bms_alarm_bit_");
            processBitChanges(
                    hideEventCodeList,
                    bmsBitCodes.getFaultCode(),
                    lastBmsBitCode.getFaultCode(),
                    bmsBitCodes.getDeviceTypeCode(),
                    deviceEntity,
                    emsStateChangeRequest.getTime(),
                    OffsetTypeEnum.BMS.getCode(),
                    "bms_fault_bit_");
        }
    }

    public void checkCellStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        List<EmsStateChangeData.AllBitCode> bitCodes =
                emsStateChangeRequest.getCurrentData().getBmsClusterBitCodes();
        List<EmsStateChangeData.AllBitCode> lastBitCodes =
                emsStateChangeRequest.getLastData().getBmsClusterBitCodes();
        for (int i = 0; i < bitCodes.size(); i++) {
            EmsStateChangeData.AllBitCode bmsBitCodes = bitCodes.get(i);
            EmsStateChangeData.AllBitCode lastBmsBitCode = lastBitCodes.get(i);

            if (!Objects.equals(bmsBitCodes, lastBmsBitCode)) {
                processBitChanges(
                        hideEventCodeList,
                        bmsBitCodes.getAlarmCode(),
                        lastBmsBitCode.getAlarmCode(),
                        bmsBitCodes.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.CLUSTER.getCode() + i,
                        "bms_cluster_{i}_alarm_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bmsBitCodes.getFaultCode(),
                        lastBmsBitCode.getFaultCode(),
                        bmsBitCodes.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.CLUSTER.getCode() + i,
                        "bms_cluster_{i}_fault_bit_");
            }
        }
    }

    public void checkPcsStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        List<EmsStateChangeData.AllBitCode> bitCodes =
                emsStateChangeRequest.getCurrentData().getPcsBitCodes();
        List<EmsStateChangeData.AllBitCode> lastBitCodes =
                emsStateChangeRequest.getLastData().getPcsBitCodes();
        for (int i = 0; i < bitCodes.size(); i++) {
            EmsStateChangeData.AllBitCode bitCode = bitCodes.get(i);
            EmsStateChangeData.AllBitCode lastBitCode = lastBitCodes.get(i);

            if (!Objects.equals(bitCode, lastBitCode)) {
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getStateCode(),
                        lastBitCode.getStateCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.PCS.getCode() + i,
                        "pcs_{i}_state_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getAlarmCode(),
                        lastBitCode.getAlarmCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.PCS.getCode() + i,
                        "pcs_{i}_alarm_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getFaultCode(),
                        lastBitCode.getFaultCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.PCS.getCode() + i,
                        "pcs_{i}_fault_bit_");
            }
        }
    }

    public void checkDcdcStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        List<EmsStateChangeData.AllBitCode> bitCodes =
                emsStateChangeRequest.getCurrentData().getDcdcBitCodes();
        List<EmsStateChangeData.AllBitCode> lastBitCodes =
                emsStateChangeRequest.getLastData().getDcdcBitCodes();
        for (int i = 0; i < bitCodes.size(); i++) {
            EmsStateChangeData.AllBitCode bitCode = bitCodes.get(i);
            EmsStateChangeData.AllBitCode lastBitCode = lastBitCodes.get(i);

            if (!Objects.equals(bitCode, lastBitCode)) {
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getStateCode(),
                        lastBitCode.getStateCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.DCDC.getCode() + i,
                        "dcdc_{i}_state_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getAlarmCode(),
                        lastBitCode.getAlarmCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.DCDC.getCode() + i,
                        "dcdc_{i}_alarm_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getFaultCode(),
                        lastBitCode.getFaultCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.PCS.getCode() + i,
                        "dcdc_{i}_fault_bit_");
            }
        }
    }

    public void checkStateEquipStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        List<EmsStateChangeData.AllBitCode> bitCodes =
                emsStateChangeRequest.getCurrentData().getStateBitCodes();
        List<EmsStateChangeData.AllBitCode> lastBitCodes =
                emsStateChangeRequest.getLastData().getStateBitCodes();
        for (int i = 0; i < bitCodes.size(); i++) {
            EmsStateChangeData.AllBitCode bitCode = bitCodes.get(i);
            EmsStateChangeData.AllBitCode lastBitCode = lastBitCodes.get(i);

            if (!Objects.equals(bitCode, lastBitCode)) {
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getStateCode(),
                        lastBitCode.getStateCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.STATES.getCode() + i,
                        "states_{i}_state_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getAlarmCode(),
                        lastBitCode.getAlarmCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.STATES.getCode() + i,
                        "states_{i}_alarm_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getFaultCode(),
                        lastBitCode.getFaultCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.STATES.getCode() + i,
                        "states_{i}_fault_bit_");
            }
        }
    }

    public void checkAirConditionStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        List<EmsStateChangeData.NoAlarmBitCode> bitCodes =
                emsStateChangeRequest.getCurrentData().getAirConditionerBitCodes();
        List<EmsStateChangeData.NoAlarmBitCode> lastBitCodes =
                emsStateChangeRequest.getLastData().getAirConditionerBitCodes();
        for (int i = 0; i < bitCodes.size(); i++) {
            EmsStateChangeData.NoAlarmBitCode bitCode = bitCodes.get(i);
            EmsStateChangeData.NoAlarmBitCode lastBitCode = lastBitCodes.get(i);

            if (!Objects.equals(bitCode, lastBitCode)) {
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getStateCode(),
                        lastBitCode.getStateCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.AIR.getCode() + i,
                        "air_conditioner_{i}_state_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getFaultCode(),
                        lastBitCode.getFaultCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.AIR.getCode() + i,
                        "air_conditioner_{i}_fault_bit_");
            }
        }
    }

    public void checkFirefightingStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        List<EmsStateChangeData.NoAlarmBitCode> bitCodes =
                emsStateChangeRequest.getCurrentData().getFirefightingBitCodes();
        List<EmsStateChangeData.NoAlarmBitCode> lastBitCodes =
                emsStateChangeRequest.getLastData().getFirefightingBitCodes();
        for (int i = 0; i < bitCodes.size(); i++) {
            EmsStateChangeData.NoAlarmBitCode bitCode = bitCodes.get(i);
            EmsStateChangeData.NoAlarmBitCode lastBitCode = lastBitCodes.get(i);

            if (!Objects.equals(bitCode, lastBitCode)) {
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getStateCode(),
                        lastBitCode.getStateCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.FIRE.getCode() + i,
                        "firefighting_{i}_state_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getFaultCode(),
                        lastBitCode.getFaultCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.FIRE.getCode() + i,
                        "firefighting_{i}_fault_bit_");
            }
        }
    }

    public void checkWaterCoolerStateChange(
            List<String> hideEventCodeList,
            EmsStateChangeRequest emsStateChangeRequest,
            DeviceEntity deviceEntity) {
        List<EmsStateChangeData.NoAlarmBitCode> bitCodes =
                emsStateChangeRequest.getCurrentData().getWaterCoolerBitCodes();
        List<EmsStateChangeData.NoAlarmBitCode> lastBitCodes =
                emsStateChangeRequest.getLastData().getWaterCoolerBitCodes();
        for (int i = 0; i < bitCodes.size(); i++) {
            EmsStateChangeData.NoAlarmBitCode bitCode = bitCodes.get(i);
            EmsStateChangeData.NoAlarmBitCode lastBitCode = lastBitCodes.get(i);

            if (!Objects.equals(bitCode, lastBitCode)) {
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getStateCode(),
                        lastBitCode.getStateCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.WATER_COOLER.getCode() + i,
                        "water_cooler_{i}_state_bit_");
                processBitChanges(
                        hideEventCodeList,
                        bitCode.getFaultCode(),
                        lastBitCode.getFaultCode(),
                        bitCode.getDeviceTypeCode(),
                        deviceEntity,
                        emsStateChangeRequest.getTime(),
                        OffsetTypeEnum.WATER_COOLER.getCode() + i,
                        "water_cooler_{i}_fault_bit_");
            }
        }
    }

    private void processBitChanges(
            List<String> hideEventCodeList,
            List<Integer> currentBits,
            List<Integer> lastBits,
            int deviceTypeCode,
            DeviceEntity deviceEntity,
            long time,
            String equipName,
            String column) {
        if (Objects.equals(currentBits, lastBits)) {
            return;
        }
        for (int i = 0; i < currentBits.size(); i++) {
            int data = currentBits.get(i);
            int lastData = lastBits.get(i);
            if (data != lastData) {
                StateChange stateChange =
                        getStateChange(
                                lastData, data, deviceEntity, time, deviceTypeCode, column + i);
                stateChange.setEquipName(equipName);
                eventService.handleEvent(hideEventCodeList, stateChange);
            }
        }
    }

    private static StateChange getStateChange(
            int lastData,
            int data,
            DeviceEntity deviceEntity,
            long time,
            int typeCode,
            String column) {
        log.info(
                "ems原始数据 projectid {} id {} name {} column{}：{}，新采集数据{}",
                deviceEntity.getProjectId(),
                deviceEntity.getId(),
                deviceEntity.getName(),
                column,
                lastData,
                data);
        StateChange stateChange = new StateChange();
        stateChange.setProjectId(deviceEntity.getProjectId());
        stateChange.setValue(data);
        stateChange.setLastValue(lastData);
        stateChange.setDeviceId(deviceEntity.getId());
        stateChange.setDeviceName(deviceEntity.getName());
        stateChange.setMaintain(deviceEntity.getMaintain());
        stateChange.setColumn(column);
        stateChange.setTime(time);
        stateChange.setTypeCode(typeCode);
        return stateChange;
    }
}
