# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

# IDE Files
.idea/
*.iml
*.ipr
*.iws
.vscode/
.settings/
.project
.classpath

# Eclipse
.apt_generated/
.classpath
.factorypath
.project
.settings/
.springBeans
.sts4-cache/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
*.cache
*.bak
*.backup

# Logs
logs/
*.log
log/
logs*/
*.logs

# Test output
test-output/
surefire-reports/
*.hprof
*.heapdump

# Application specific
application-local.yml
application-local.properties
config/local/
.env
.env.local
.env.*.local

# Database
*.db
*.sqlite
*.sqlite3

# Node.js (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache

# Python
__pycache__/
*.py[cod]
*$py.class
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Redis dump
dump.rdb

# InfluxDB
*.influxdb
*.flux

# Docker
.dockerignore

# Kubernetes
helm/
.kube/

# Monitoring and profiling
*.prof
*.pprof
*.trace

# Cache
.cache/
.cache*/
*.cache

# Local configuration overrides
application-override.yml
application-override.properties

# Secret files (should never be committed)
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore
secrets/
credentials/

# Backup files
*.backup
*.bak
*.orig

# Temp directories
tmp/
temp/
.tmp/

# Documentation build
docs/_build/
docs/build/

# Coverage reports
coverage/
.coverage/
htmlcov/
.coverage.*
*.cover

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Spring Boot
spring-boot-app.jar
spring-boot-app.run/
application-dev.yml
application-test.yml
application-pro.yml
application-local.yml

# MyBatis Generator
mybatis-generator.xml
mybatis-generator.properties

# Swagger UI
swagger-ui/

# Actuator endpoints (sensitive)
actuator/
health/
metrics/
info/

# Temporary upload files
uploads/
upload/
temp-uploads/

# Database migrations (if using Flyway or Liquibase)
.liquibase/
flyway/
migrations/

# Performance testing
jmeter/
*.jmx
*.jtl

# Build artifacts
build/
dist/
out/
artifact/
artifacts/

# Development tools
.redux/
.sourcemaps/
*.map

# Linting
.jshint/
.jscs/
.tslint/
.eslintignore

# Testing
.cypress/
playwright-report/
test-results/
playwright/.cache/

# Application specific data
data/
exports/
imports/
reports/

# Monitoring and logging
prometheus/
grafana/
elasticsearch/
kibana/

CLAUDE.md