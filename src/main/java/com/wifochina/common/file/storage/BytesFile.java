package com.wifochina.common.file.storage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

/**
 * 磁盘文件
 * <AUTHOR> 
 */
public class BytesFile implements MultipartFile, Serializable {
    private static final long serialVersionUID = 1L;
    protected static final Logger logger = LoggerFactory.getLogger(BytesFile.class);
    private String name, originalFilename, contentType;
    private byte[] bytes;

    public BytesFile(MultipartFile multipartFile) throws IOException {
        name = multipartFile.getName();
        originalFilename = multipartFile.getOriginalFilename();
        contentType = multipartFile.getContentType();
        bytes = multipartFile.getBytes();
    }
    
    public BytesFile(byte[] bytes, String originalFilename) throws IOException {
        this.bytes = bytes;
        this.originalFilename = originalFilename;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }

    @Override
    public String getContentType() {
        return contentType;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return bytes;
    }

    @Override
    public boolean isEmpty() {
        return getSize() == 0;
    }

    @Override
    public long getSize() {
        return bytes.length;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(bytes);
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        try (FileOutputStream fout = new FileOutputStream(dest)) {
            fout.write(bytes);
        }
    }
}
