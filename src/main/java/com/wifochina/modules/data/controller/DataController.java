package com.wifochina.modules.data.controller;

import com.wifochina.common.page.Result;
import com.wifochina.modules.data.entity.*;
import com.wifochina.modules.data.listener.MeterStateChange;
import com.wifochina.modules.data.listener.StateChange;
import com.wifochina.modules.data.request.StateChangeRequest;
import com.wifochina.modules.data.service.EventService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.DeviceEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 电表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-19
 */
@RestController
@Api(tags = "data收集")
@RequestMapping("/data")
@Slf4j
public class DataController {

    @Resource private DeviceService deviceService;

    @Resource private AmmeterService ammeterService;

    @Resource private EventService eventService;

    @Resource private HideEventCodeService hideEventCodeService;

    /** 判断可能事件发生(事件状态发生变化) data为空则未采集到数据 */
    @PostMapping("/change")
    @ApiOperation("ems事件变化")
    public Result<T> change(@RequestBody StateChangeRequest stateChangeRequest) {
        stateChangeRequest.setTime(
                String.valueOf(stateChangeRequest.getTime()).length() == 13
                        ? stateChangeRequest.getTime()
                        : stateChangeRequest.getTime() * 1000);
        // 判断可能事件发生(事件状态发生变化) data为空则未采集到数据
        int[] data = stateChangeRequest.getCurrentData();
        int[] lastData = stateChangeRequest.getLastData();
        long time = stateChangeRequest.getTime();
        String deviceId = stateChangeRequest.getDeviceId();
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        if (deviceEntity == null) {
            return Result.success();
        }
        List<String> hideEventCodes =
                hideEventCodeService.getEmsHideEventCode(deviceEntity.getProjectId());
        if (lastData == null || lastData.length == 0 || data == null || data.length == 0) {
            return Result.success();
        }
        // 循环事件点位监控数据，如果发生前后状态的变化，再进行事件通知
        for (PointDataEntity pointDataEntity : eventService.getPointDataEventList()) {
            String offset = pointDataEntity.getPointOffset();
            // 如果有 * 号，则点位需要计算
            if (offset.contains("*")) {
                // 不记录bms_cluster事件变化的情况 2024-05-10号 吉总确认
                if (pointDataEntity.getPointColumn().contains("bms_cluster_{i}_state")) {
                    continue;
                }
                // 获取装备类型 offset值示例（100+AIRi*10）-》AIR
                String equipType =
                        offset.substring(offset.indexOf("+") + 1, offset.indexOf("*") - 1);
                int countIndex = eventService.getEquipCountIndexMap().get(equipType.trim());
                // 返回装备实际数量
                int count = data[countIndex];
                // 初始位置
                int addressInit = pointDataEntity.getPointAddress();
                // 计算出offset的起点位置
                int offsetStart = Integer.parseInt(offset.substring(0, offset.indexOf("+")).trim());
                // 计算出offset的倍数
                int multiple = Integer.parseInt(offset.substring(offset.indexOf("*") + 1).trim());
                // 循环遍历处理该设备下的所有装备
                for (int i = 0; i < count; i++) {
                    int index = addressInit + offsetStart + i * multiple;
                    String diff = Integer.toBinaryString(lastData[index] ^ data[index]);
                    if (!"0".equals(diff)) {
                        StateChange stateChange =
                                eventService.getStateChange(
                                        pointDataEntity,
                                        lastData,
                                        index,
                                        data,
                                        deviceEntity,
                                        deviceId,
                                        time,
                                        i);
                        stateChange.setEquipName(equipType + i);
                        eventService.handleEvent(hideEventCodes, stateChange);
                    }
                }
            } else {
                // 系统状态
                int index =
                        pointDataEntity.getPointAddress()
                                + Integer.parseInt(pointDataEntity.getPointOffset().trim());
                String diff = Integer.toBinaryString(lastData[index] ^ data[index]);
                if (!"0".equals(diff)) {
                    StateChange stateChange =
                            eventService.getStateChange(
                                    pointDataEntity,
                                    lastData,
                                    index,
                                    data,
                                    deviceEntity,
                                    deviceId,
                                    time,
                                    -1);
                    if (pointDataEntity.getPointColumn().startsWith("bms")) {
                        stateChange.setEquipName(OffsetTypeEnum.BMS.getCode());
                    } else {
                        stateChange.setEquipName("EMS");
                    }
                    eventService.handleEvent(hideEventCodes, stateChange);
                }
            }
        }
        return Result.success();
    }

    /** 判断可能事件发生(事件状态发生变化) data为空则未采集到数据 */
    @PostMapping("/meterChange")
    @ApiOperation("电表事件变化")
    public Result<T> collectMeterDataPoint(@RequestBody StateChangeRequest stateChangeRequest) {
        stateChangeRequest.setTime(
                String.valueOf(stateChangeRequest.getTime()).length() == 13
                        ? stateChangeRequest.getTime()
                        : stateChangeRequest.getTime() * 1000);
        String meterId = stateChangeRequest.getDeviceId();
        AmmeterEntity ammeterEntity = ammeterService.getById(meterId);
        if (ammeterEntity == null) {
            return Result.success();
        }
        int[] data = stateChangeRequest.getCurrentData();
        int[] lastData = stateChangeRequest.getLastData();
        if (lastData == null || lastData.length == 0 || data == null || data.length == 0) {
            return Result.success();
        }
        List<String> hideEventCodes =
                hideEventCodeService.getMeterHideEventCode(ammeterEntity.getProjectId());
        for (int i = 0; i < data.length; i++) {
            String diff = Integer.toBinaryString(lastData[i] ^ data[i]);
            if ("0".equals(diff)) {
                continue;
            }
            log.info("原始数据：{}，新采集数据{}", lastData[i], data[i]);
            MeterStateChange meterStateChange = new MeterStateChange();
            meterStateChange.setMeterId(ammeterEntity.getId());
            meterStateChange.setMeterName(ammeterEntity.getName());
            meterStateChange.setBinaryString(diff);
            meterStateChange.setValue(data[i]);
            meterStateChange.setLastValue(lastData[i]);
            meterStateChange.setIndex(i);
            meterStateChange.setMaintain(ammeterEntity.getMaintain());
            meterStateChange.setProjectId(ammeterEntity.getProjectId());
            meterStateChange.setMeterType(ammeterEntity.getVendor());
            meterStateChange.setTime(stateChangeRequest.getTime());
            eventService.handleMeterEvent(hideEventCodes, meterStateChange);
        }
        return Result.success();
    }
}
