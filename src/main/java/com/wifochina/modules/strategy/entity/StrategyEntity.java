package com.wifochina.modules.strategy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wifochina.modules.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_strategy")
@ApiModel(value="StrategyEntity对象", description="StrategyEntity对象")
public class StrategyEntity extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "策略类型  0充电 1放电 2自发自用")
    private Integer type;

    @ApiModelProperty(value = "星期几 1代表星期一 2代表星期二 ... 7代表星期日")
    private Integer weekDay;

    @ApiModelProperty(value = "功率")
    private Integer power;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat( pattern = "HH:mm")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat( pattern = "HH:mm")
    private LocalTime endTime;

    @ApiModelProperty(value = "需量控制功率")
    private Double demandPower;

    @ApiModelProperty(value = "需量控制功率,并网点控制目标功率(kW)")
    private Double controlPower;

    @ApiModelProperty(value = "放电soc下限")
    private Double soc;

    @ApiModelProperty(value = "防逆流 true 为勾选 false 为未勾选")
    private Boolean antiReflux;

    @ApiModelProperty(value = "防逆流最小值")
    private Double backFlowLimitPower;

    @ApiModelProperty(value = "并网点设计容量(kVA)")
    private Double pccDemandPower;

    @ApiModelProperty(value = "分组id")
    private String groupId;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "优先级")
    private Integer priority;


}
