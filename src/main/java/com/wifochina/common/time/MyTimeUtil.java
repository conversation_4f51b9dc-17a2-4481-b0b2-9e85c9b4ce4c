package com.wifochina.common.time;

import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.util.ServiceAssert;

import lombok.Data;

import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * @since 5/20/2022 6:16 PM
 * <AUTHOR>
 * @version 1.0
 */
public class MyTimeUtil {
    public static final long ONE_DAY_SECONDS = 86400;
    public static final int WEEK_DAY = 7;

    public static final long GMT8_SECONDS = 28800;

    /**
     * 获取今日零点时间 带时区
     *
     * @param timeZoneCode : 时区代码
     * @return : long
     */
    public static long getTodayZeroTime(String timeZoneCode) {
        ZoneId zoneId = ZoneId.of(timeZoneCode);
        LocalDate today = LocalDate.now(zoneId);
        ZonedDateTime todayAtMidnightInZone = today.atStartOfDay(zoneId);
        return todayAtMidnightInZone.toInstant().getEpochSecond();
    }

    public static long getOneDayZeroTime(Long time, String timeZoneCode) {
        ZoneId timeZone = ZoneId.of(timeZoneCode);
        // 将时间戳转换为UTC时区的ZonedDateTime
        ZonedDateTime utcDateTime =
                ZonedDateTime.ofInstant(Instant.ofEpochSecond(time), ZoneOffset.UTC);
        // 转换到目标时区
        ZonedDateTime targetDateTime = utcDateTime.withZoneSameInstant(timeZone);
        // 获取日期，并找到该日期在目标时区的午夜时刻
        LocalDate date = targetDateTime.toLocalDate();
        ZonedDateTime midnight = date.atStartOfDay(timeZone);
        // 返回目标时区午夜时间的时间戳（以秒为单位）
        return midnight.toEpochSecond();
    }

    /**
     * 获取当前月份第一天的零点时间 带时区
     *
     * @param timeZoneCode: 时区
     * @return : long
     */
    public static long getCurrentMonthZeroTime(String timeZoneCode) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now(getZoneOffsetFromZoneCode(timeZoneCode));
        // 获取当前月份的第一天
        LocalDate firstDayOfMonth = LocalDate.of(currentDate.getYear(), currentDate.getMonth(), 1);
        // 设置时间为零点
        LocalDateTime startOfMonth = firstDayOfMonth.atTime(LocalTime.MIDNIGHT);
        return startOfMonth.atOffset(getZoneOffsetFromZoneCode(timeZoneCode)).toEpochSecond();
    }

    /**
     * 返回timeToCheck 是否在startTime和endTime范围内
     *
     * @param timeToCheck : timeToCheck
     * @param startTime : startTime
     * @param endTime : endTime
     * @return : boolean
     */
    public static boolean isWithinTimeRange(
            LocalTime timeToCheck, LocalTime startTime, LocalTime endTime) {
        return !timeToCheck.isBefore(startTime) && !timeToCheck.isAfter(endTime);
    }

    /**
     * 获取最近7天的零点时间 带时区
     *
     * @param timeZoneCode: 时区
     * @return : long
     */
    public static List<DayHolder> getOneWeekTime(String timeZoneCode) {
        List<DayHolder> dayHolders = new ArrayList<>();
        ZoneOffset zoneOffset = getZoneOffsetFromZoneCode(timeZoneCode);
        ZonedDateTime currentDateTime = ZonedDateTime.now(zoneOffset);
        for (int i = 0; i < WEEK_DAY; i++) {
            ZonedDateTime startOfDay = currentDateTime.toLocalDate().atStartOfDay(zoneOffset);
            ZonedDateTime endOfDay =
                    currentDateTime.toLocalDate().atTime(23, 59, 59).atZone(zoneOffset);
            // 将日期时间范围格式化为字符串并添加到列表中
            dayHolders.add(new DayHolder(startOfDay, endOfDay));
            // 将日期向前推一天
            currentDateTime = currentDateTime.minusDays(1);
        }
        return dayHolders;
    }

    /**
     * 获取今天的 23:59:59 时间戳 带duration
     *
     * @param timeZoneCode: 时区
     * @return long
     */
    public static long getTodayEndTime(String timeZoneCode) {
        ZoneOffset zoneOffset = getZoneOffsetFromZoneCode(timeZoneCode);
        ZonedDateTime currentDateTime = ZonedDateTime.now(zoneOffset);
        ZonedDateTime endOfDay =
                currentDateTime.toLocalDate().atTime(23, 59, 59).atZone(zoneOffset);
        return endOfDay.toEpochSecond();
    }

    /**
     * 判断给定的时间戳 是否当前年份的
     *
     * @param time : 给定的时间戳
     * @param timeZoneCode : 时区
     * @return : boolean 是否当前年份
     */
    public static boolean isInCurrentYear(Long time, String timeZoneCode, TimeUnit timeUnit) {
        Instant instant;
        if (timeUnit == TimeUnit.MILLISECONDS) {
            instant = Instant.ofEpochMilli(time);
        } else {
            instant = Instant.ofEpochSecond(time);
        }
        LocalDate localDate = instant.atZone(getZoneOffsetFromZoneCode(timeZoneCode)).toLocalDate();
        int currentYear = LocalDate.now(getZoneOffsetFromZoneCode(timeZoneCode)).getYear();
        int year = localDate.getYear();
        return currentYear == year;
    }

    /**
     * 给一个时间范围 得到这个时间范围内的 零点时间戳 比如 long startDate = 1699582508L; 2023-11-10 10:15:08 long endDate =
     * 1699927207L; 2023-11-14 10:00:07 得到list // @formatter:off 1699545600 : 2023-11-10 00:00:00
     * 1699632000 : 2023-11-11 00:00:00 1699718400 : 2023-11-12 00:00:00 1699804800 : 2023-11-13
     * 00:00:00 1699891200 : 2023-11-14 00:00:00 // @formatter:on
     *
     * @param startDate :开始时间
     * @param endDate : 结束时间
     * @return : java.util.List<java.lang.Long> 零点时间戳
     */
    public static List<Long> splitRangeToMidnights(
            long startDate, long endDate, String timeZoneCode) {
        List<Long> midnightTimestamps = new ArrayList<>();
        // 将时间戳转换为 LocalDateTime，以便获取日期部分
        LocalDateTime startDateTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(startDate), getZoneOffsetFromZoneCode(timeZoneCode));
        LocalDateTime endDateTime =
                LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(endDate), getZoneOffsetFromZoneCode(timeZoneCode));
        // 拆分日期范围
        LocalDateTime currentDate = startDateTime;
        while (!currentDate.isAfter(endDateTime)) {
            // 获取当前日期的零点时间
            LocalDateTime midnight = currentDate.withHour(0).withMinute(0).withSecond(0);
            // 将零点时间转换为时间戳并添加到列表
            midnightTimestamps.add(
                    midnight.atZone(getZoneOffsetFromZoneCode(timeZoneCode)).toEpochSecond());
            // 下一天
            currentDate = midnight.plusDays(1);
        }

        return midnightTimestamps;
    }

    @Data
    public static class DayHolder {
        private ZonedDateTime startZonedDateTime;
        private ZonedDateTime endZonedDateTime;

        public DayHolder(ZonedDateTime startZonedDateTime, ZonedDateTime endZonedDateTime) {
            this.startZonedDateTime = startZonedDateTime;
            this.endZonedDateTime = endZonedDateTime;
        }
    }

    public static ZoneOffset getZoneOffsetFromZoneCode(String timeZoneCode) {
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneCode);
        int offsetSeconds = timeZone.getRawOffset();
        return ZoneOffset.ofTotalSeconds(offsetSeconds / 1000);
    }

    public static long getOffsetSecondsFromZoneCode(String timeZoneCode) {
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneCode);
        int offsetSeconds = timeZone.getRawOffset();
        return offsetSeconds / 1000;
    }

    public static void checkTime(Long start, Long end) {
        // 开始时间不晚于结束时间，否则返回时间错误异常
        ServiceAssert.isTrue(end.compareTo(start) >= 0, ErrorResultCode.TIME_OVERLAPPED.value());
    }
}
