package com.wifochina.modules.alarm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import com.wifochina.modules.alarm.mapper.AlarmConfigMapper;
import com.wifochina.modules.alarm.service.AlarmConfigService;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 告警配置 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class AlarmConfigServiceImpl extends ServiceImpl<AlarmConfigMapper, AlarmConfigEntity>
        implements AlarmConfigService {

    @Override
    public List<AlarmConfigEntity> getByProjectId(String projectId) {
        LambdaQueryWrapper<AlarmConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlarmConfigEntity::getProjectId, projectId);
        queryWrapper.orderByDesc(AlarmConfigEntity::getCreateTime);
        return this.list(queryWrapper);
    }
}
