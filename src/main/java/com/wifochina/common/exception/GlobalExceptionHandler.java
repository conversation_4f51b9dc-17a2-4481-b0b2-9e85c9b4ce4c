package com.wifochina.common.exception;


import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.page.Result;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MultipartException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.nio.file.AccessDeniedException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 全局 验证 异常 处理
 */
@ControllerAdvice(annotations = {RestController.class, Controller.class})
public class GlobalExceptionHandler {

    @Value("${spring.profiles.active:dev}")
    public String activeProfile;

    /**
     * 构建返回错误
     *
     * @param status
     * @return
     */
    private Result<?> failure(ErrorResultCode status) {
        return failure(status, null);
    }

    /**
     * 构建返回错误
     *
     * @param status
     * @param data
     * @return
     */
    private Result<?> failure(ErrorResultCode status, Object data) {
        return Result.failure(status.value(), data);
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<?> handleValidationException(ConstraintViolationException e) {
        List<Map<String, Object>> fields = new ArrayList<Map<String, Object>>();
        for (ConstraintViolation<?> cv : e.getConstraintViolations()) {
            String fieldName = ((PathImpl) cv.getPropertyPath()).getLeafNode().asString();
            String message = cv.getMessage();
            Map<String, Object> field = new HashMap<String, Object>(2);
            field.put("field", fieldName);
            field.put("message", message);
            fields.add(field);
        }
        return failure(ErrorResultCode.ILLEGAL_DATA, fields);
    }

    @ResponseBody
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleBindException(BindException e) {
        List<Map<String, Object>> fields = new ArrayList<Map<String, Object>>();
        for (FieldError error : e.getFieldErrors()) {
            Map<String, Object> field = new HashMap<String, Object>(2);
            field.put("field", error.getField());
            field.put("message", error.getDefaultMessage());
            fields.add(field);
        }
        return failure(ErrorResultCode.ILLEGAL_DATA, fields);
    }

    @ResponseBody
    @ExceptionHandler(MultipartException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleMultipartException() {
        return failure(ErrorResultCode.MULTIPART_TOO_LARGE);
    }

    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleIllegalArgumentException(IllegalArgumentException e) {
        return failure(ErrorResultCode.ILLEGAL_ARGUMENT);
    }

    @ResponseBody
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleMissingServletRequestParameterException(
            MissingServletRequestParameterException e) {
        return failure(ErrorResultCode.MISSING_ARGUMENT);
    }

    @ResponseBody
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleMethodArgumentTypeMismatchExceptionException(
            MethodArgumentTypeMismatchException e) {

        return failure(ErrorResultCode.ILLEGAL_ARGUMENT_TYPE);
    }

    @ResponseBody
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<?> handleException(AccessDeniedException e) {
        e.printStackTrace();
        return failure(ErrorResultCode.UNAUTHORIZED);
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleException(Exception e) {
        e.printStackTrace();
        return failure(ErrorResultCode.INTERNAL_SERVER_ERROR);
    }


    @ResponseBody
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Result<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        return failure(ErrorResultCode.METHOD_NOT_ALLOWED);
    }

    @ResponseBody
    @ExceptionHandler(ServiceException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleServiceException(ServiceException e) {
        if (e.getCode() == null) {
            return failure(ErrorResultCode.SERVICE_EXCEPTION);
        } else {
            return Result.failure(e.getCode(), e.getMessage());
        }
    }

    @ResponseBody
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleServiceException(HttpMessageNotReadableException e) {
        e.printStackTrace();
        return Result.failure(ErrorResultCode.ILLEGAL_ARGUMENT.value(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(HttpMessageConversionException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleServiceException(HttpMessageConversionException e) {
        e.printStackTrace();
        return Result.failure(ErrorResultCode.ILLEGAL_ARGUMENT.value(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(IllegalStateException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<?> handleIllegalStateException(IllegalStateException e) {
        return failure(ErrorResultCode.ILLEGAL_STATE);
    }

    @ResponseBody
    @ExceptionHandler(HttpClientErrorException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleBadRequestException(HttpClientErrorException e) {
        return Result.failure(ErrorResultCode.ILLEGAL_ARGUMENT.value(), e.getResponseBodyAsString());
    }


}
