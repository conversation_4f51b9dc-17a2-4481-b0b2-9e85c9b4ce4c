package com.wifochina.modules.data.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.CommonConstants;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.alarm.service.AlarmHandler;
import com.wifochina.modules.data.entity.OffsetTypeEnum;
import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.entity.PointDataType;
import com.wifochina.modules.data.entity.TypeCodeIndexEnum;
import com.wifochina.modules.data.listener.MeterStateChange;
import com.wifochina.modules.data.listener.StateChange;
import com.wifochina.modules.data.service.EventService;
import com.wifochina.modules.data.service.PointDataService;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.service.EventCodeService;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.event.service.MeterEventCodeService;
import com.wifochina.modules.group.entity.DeviceEntity;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Service
public class EventServiceImpl implements EventService {

    @Resource private EventMessageService eventMessageService;

    @Resource private PointDataService pointDataService;

    @Resource private EventCodeService eventCodeService;

    @Resource private MeterEventCodeService meterEventCodeService;

    @Resource private AlarmHandler alarmHandler;

    @Resource private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /** 事件点位的列表，只查找bit位的数据 */
    @Getter private List<PointDataEntity> pointDataEventList;

    /** 设备数目所在索引集合，以OffsetTypeEnum中CLUSTER，PCS，AiR，FIRE，STACK，CELL作为key进行存储。 */
    @Getter public Map<String, Integer> equipCountIndexMap = new HashMap<>();

    public static Map<String, Map<Integer, EventCodeEntity>> eventCodeMap = new HashMap<>();

    public static Map<String, MeterEventCodeEntity> meterEventCodeMap = new HashMap<>();

    public static Map<String, PointDataEntity> evventPointDataMap = new HashMap<>();

    @PostConstruct
    private void initPointDataEventMap() {
        for (OffsetTypeEnum offsetType : OffsetTypeEnum.values()) {
            PointDataEntity pointDataEntity =
                    pointDataService.getOne(
                            Wrappers.lambdaQuery(PointDataEntity.class)
                                    .eq(PointDataEntity::getPointColumn, offsetType.getColumn()));
            equipCountIndexMap.put(
                    offsetType.getCode(),
                    pointDataEntity.getPointAddress()
                            + Integer.parseInt(pointDataEntity.getPointOffset()));
        }
        List<EventCodeEntity> eventCodeEntityList = eventCodeService.list();
        // 将对象按照column分组
        Map<String, List<EventCodeEntity>> codeMap =
                eventCodeEntityList.stream()
                        .collect(
                                Collectors.groupingBy(
                                        item ->
                                                item.getDeviceTypeCode()
                                                        + "_"
                                                        + item.getPointColumn()));
        for (String key : codeMap.keySet()) {
            List<EventCodeEntity> list = codeMap.get(key);
            Map<Integer, EventCodeEntity> map =
                    list.stream()
                            .collect(
                                    Collectors.toMap(
                                            EventCodeEntity::getBitValue,
                                            eventCodeEntity -> eventCodeEntity));
            eventCodeMap.put(key, map);
        }
        List<MeterEventCodeEntity> meterEventCodeEntities =
                meterEventCodeService
                        .lambdaQuery()
                        .eq(MeterEventCodeEntity::getDigital0Analog1Control2, 0)
                        .list();
        meterEventCodeMap =
                meterEventCodeEntities.stream()
                        .collect(
                                Collectors.toMap(
                                        e ->
                                                e.getProjectId()
                                                        + "_"
                                                        + e.getMeterType()
                                                        + "_"
                                                        + e.getBitOffset()
                                                        + "_"
                                                        + e.getBitValue(),
                                        code -> code));
        pointDataEventList =
                pointDataService.list(
                        Wrappers.lambdaQuery(PointDataEntity.class)
                                .eq(PointDataEntity::getPointType, PointDataType.BIT));
        evventPointDataMap =
                pointDataEventList.stream()
                        .collect(Collectors.toMap(PointDataEntity::getPointColumn, e -> e));
    }

    public StateChange getStateChange(
            PointDataEntity pointDataEntity,
            int[] lastData,
            int index,
            int[] data,
            DeviceEntity deviceEntity,
            String deviceId,
            long time,
            int i) {
        log.info("原始数据：{}，新采集数据{}", lastData[index], data[index]);
        StateChange stateChange = new StateChange();
        stateChange.setProjectId(deviceEntity.getProjectId());
        stateChange.setValue(data[index]);
        stateChange.setLastValue(lastData[index]);
        stateChange.setDeviceId(deviceId);
        stateChange.setDeviceName(deviceEntity.getName());
        stateChange.setMaintain(deviceEntity.getMaintain());
        stateChange.setColumn(pointDataEntity.getPointColumn());
        stateChange.setTime(time);
        // 0代表system
        int typeCode = 0;
        if (pointDataEntity.getPointColumn().contains(TypeCodeIndexEnum.PCS.getCode())) {
            // 2代表psc
            typeCode = TypeCodeIndexEnum.PCS.getIndex();
        } else if (pointDataEntity.getPointColumn().contains(TypeCodeIndexEnum.BMS.getCode())) {
            // 4代表bms
            typeCode = TypeCodeIndexEnum.BMS.getIndex();
        } else if (pointDataEntity.getPointColumn().contains(TypeCodeIndexEnum.AIR.getCode())) {
            // 6代表air
            typeCode = TypeCodeIndexEnum.AIR.getIndex();
        } else if (pointDataEntity.getPointColumn().contains(TypeCodeIndexEnum.FIRE.getCode())) {
            // 8代表fire
            typeCode = TypeCodeIndexEnum.FIRE.getIndex();
        } else if (pointDataEntity
                .getPointColumn()
                .contains(TypeCodeIndexEnum.WATER_COOLER.getCode())) {
            // 15代表water_cooler
            typeCode = TypeCodeIndexEnum.WATER_COOLER.getIndex();
        } else if (pointDataEntity.getPointColumn().contains(TypeCodeIndexEnum.DCDC.getCode())) {
            // 20001代表dcdc
            typeCode = TypeCodeIndexEnum.DCDC.getIndex();
        }
        if (pointDataEntity.getPointColumn().contains(TypeCodeIndexEnum.STATES.getCode())) {
            typeCode = 21100 + i * 50;
        }
        stateChange.setTypeCode(data[typeCode]);
        return stateChange;
    }

    public void handleEvent(List<String> eventCodeList, StateChange stateChange) {
        // 具体的某个事件
        Map<Integer, EventCodeEntity> eventBitMap =
                eventCodeMap.get(stateChange.getTypeCode() + "_" + stateChange.getColumn());
        // 事件对象
        EventMessageEntity eventMessageEntity = new EventMessageEntity();
        // 设备id
        eventMessageEntity.setDeviceId(stateChange.getDeviceId());
        // 装备名称
        eventMessageEntity.setEquipName(stateChange.getEquipName());
        // 是否维护
        eventMessageEntity.setMaintain(stateChange.getMaintain());
        // i=15 代表16位数据最大位
        for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
            if ((stateChange.getValue() & (1 << i)) == (stateChange.getLastValue() & (1 << i))) {
                continue;
            }
            if (eventBitMap == null) {
                log.error("事件column:{}的bitmap为空", stateChange.getColumn());
                return;
            }
            EventCodeEntity eventCodeEntity = eventBitMap.get(i);
            if (eventCodeEntity == null) {
                log.error("事件column为{}的第{}位发生变化，但却没有找到对应的事件", stateChange.getColumn(), i);
                continue;
            }
            // 事件描述 Fault、State、Alarm
            // (stateChange.getValue()&(1<<i))==0?0:1) 如果位运算结果不等于0，证明改位为1
            if (eventCodeEntity.getBitStand()
                    == ((stateChange.getValue() & (1 << i)) == 0 ? 0 : 1)) {
                eventMessageEntity.setEventDescription(
                        eventCodeEntity.getEventDescription().trim());
                eventMessageEntity.setEventOnOff("发生");
                if ("Fault".equals(eventCodeEntity.getEventLevel())
                        || "Alarm".equals(eventCodeEntity.getEventLevel())) {
                    // 如果发生了警告和错误，让用户确认
                    eventMessageEntity.setStatus(0);
                } else {
                    eventMessageEntity.setStatus(1);
                }
            } else {
                eventMessageEntity.setEventDescription(
                        eventCodeEntity.getEventDescription().trim());
                eventMessageEntity.setEventOnOff("消失");
                eventMessageEntity.setStatus(1);
            }
            // 事件编码=偏移量+bit码
            // 事件编码=typeCode_eventType_偏移量+bit码
            eventMessageEntity.setEventCode(
                    eventCodeEntity.getBitOffset() + eventCodeEntity.getBitValue());
            // 装备类型：派能高压电池箱、汇川630kWPCS
            eventMessageEntity.setEquipType(eventCodeEntity.getDeviceType());
            // 事件类型State、Fault、Alarm
            eventMessageEntity.setEventType(eventCodeEntity.getEventLevel());
            eventMessageEntity.setEventDescriptionEn(eventCodeEntity.getEventDescriptionEn());
            eventMessageEntity.setProjectId(stateChange.getProjectId());
            eventMessageEntity.setCreateTime(stateChange.getTime());
            eventMessageEntity.setUpdateTime(stateChange.getTime());
            eventMessageEntity.setEventKey(eventCodeEntity.getEventCode());
            eventMessageEntity.setWhetherHide(
                    eventCodeList.contains(eventCodeEntity.getEventCode()));
            // 保存事件数据
            eventMessageService.save(eventMessageEntity);
            log.info("ems原始数据 {}", stateChange);
            // 异步触发告警
            if (EventLevelEnum.FAULT.getLevel().equals(eventCodeEntity.getEventLevel())
                    || EventLevelEnum.ALARM.getLevel().equals(eventCodeEntity.getEventLevel())) {
                threadPoolTaskExecutor.execute(() -> alarmHandler.handleAlarm(false,
                        eventCodeEntity.getDeviceType(), eventMessageEntity));
            } else if (EventLevelEnum.STATE.getLevel().equals(eventCodeEntity.getEventLevel())
                    && CommonConstants.isStopEvent(eventMessageEntity.getEventKey())) {
                threadPoolTaskExecutor.execute(() -> alarmHandler.handleAlarm(false,
                        eventCodeEntity.getDeviceType(), eventMessageEntity));
            }
        }
    }

    public void handleMeterEvent(List<String> eventCodeList, MeterStateChange meterStateChange) {
        log.info(meterStateChange.toString());
        // i=15 代表16位数据最大位
        for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
            // 事件对象
            EventMessageEntity eventMessageEntity = new EventMessageEntity();
            // 设备id
            eventMessageEntity.setDeviceId(meterStateChange.getMeterId());
            // 装备名称
            eventMessageEntity.setEquipName("METER");
            // 是否维护
            eventMessageEntity.setMaintain(meterStateChange.getMaintain());
            if ((meterStateChange.getValue() & (1 << i))
                    == (meterStateChange.getLastValue() & (1 << i))) {
                continue;
            }
            MeterEventCodeEntity meterEventCodeEntity =
                    meterEventCodeMap.get(
                            meterStateChange.getProjectId()
                                    + "_"
                                    + meterStateChange.getMeterType()
                                    + "_"
                                    + meterStateChange.getIndex()
                                    + "_"
                                    + i);
            if (meterEventCodeEntity == null) {
                continue;
            }
            // 事件描述 Fault、State、Alarm
            // (stateChange.getValue()&(1<<i))==0?0:1) 如果位运算结果不等于0，证明改位为1
            if ((meterStateChange.getValue() & (1 << i)) != 0) {
                eventMessageEntity.setEventOnOff("on");
                if ("Fault".equals(meterEventCodeEntity.getEventLevel())
                        || "Alarm".equals(meterEventCodeEntity.getEventLevel())) {
                    // 如果发生了警告和错误，让用户确认
                    eventMessageEntity.setStatus(0);
                } else {
                    eventMessageEntity.setStatus(1);
                }
            } else {
                eventMessageEntity.setEventOnOff("off");
                eventMessageEntity.setStatus(1);
            }
            eventMessageEntity.setEventDescription(meterEventCodeEntity.getDescription().trim());
            Optional.ofNullable(meterEventCodeEntity.getDescriptionEn())
                    .ifPresent(
                            (descriptionEn) ->
                                    eventMessageEntity.setEventDescriptionEn(descriptionEn.trim()));
            // 事件编码=typeCode_eventType_偏移量+bit码
            eventMessageEntity.setEventCode(
                    meterEventCodeEntity.getBitOffset() + meterEventCodeEntity.getBitValue());
            // 装备类型：派能高压电池箱、汇川630kW PCS
            eventMessageEntity.setEquipType(meterEventCodeEntity.getMeterType());
            // 事件类型State、Fault、Alarm
            eventMessageEntity.setEventType(meterEventCodeEntity.getEventLevel());
            eventMessageEntity.setProjectId(meterStateChange.getProjectId());
            eventMessageEntity.setCreateTime(meterStateChange.getTime());
            eventMessageEntity.setUpdateTime(meterStateChange.getTime());
            eventMessageEntity.setEventKey(meterEventCodeEntity.getEventCode());
            eventMessageEntity.setWhetherHide(
                    eventCodeList.contains(meterEventCodeEntity.getEventCode())); // 保存事件数据
            eventMessageService.save(eventMessageEntity);
            log.info("电表原始数据数据{}", meterStateChange);
            // 异步触发告警
            if (EventLevelEnum.FAULT.getLevel().equals(meterEventCodeEntity.getEventLevel())
                    || EventLevelEnum.ALARM.getLevel().equals(meterEventCodeEntity.getEventLevel())) {
                threadPoolTaskExecutor.execute(() -> alarmHandler.handleAlarm(true, null, eventMessageEntity));
            }
        }
    }
}
