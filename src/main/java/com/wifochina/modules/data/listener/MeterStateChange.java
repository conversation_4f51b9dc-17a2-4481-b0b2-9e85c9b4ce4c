package com.wifochina.modules.data.listener;

import lombok.Data;
import lombok.ToString;

/**
 * @since  7/19/2023 4:34 PM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ToString
public class MeterStateChange {
    /**
     * 数据状态发生变化后的位运算结果
     */
    private String binaryString;

    /**
     * 状态当前值
     */
    private int value;

    /**
     * 状态旧值
     */
    private int lastValue;

    /**
     * 所属设备
     */
    private String meterId;

    /**
     * 所属设备
     */
    private String meterType;

    /**
     * 所属设备名称
     */
    private String meterName;

    /**
     * 设备是否维护
     */
    private Boolean maintain;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * code偏移量
     */
    private Integer index;

    /**
     * 时间(毫秒)
     */
    private long time;
}
