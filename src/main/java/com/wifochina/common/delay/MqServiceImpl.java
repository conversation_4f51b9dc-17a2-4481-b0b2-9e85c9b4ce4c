package com.wifochina.common.delay;

import com.weihengtech.alert.client.AlertApiClient;
import com.weihengtech.alert.model.base.BaseAlert;
import com.weihengtech.alert.model.dto.AlertCreateDTO;
import com.wifochina.common.constants.AlarmContentEnum;
import com.wifochina.common.constants.AlarmLevelEnum;
import com.wifochina.common.constants.CommonConstants;
import com.wifochina.common.runner.DelayQueueInit;
import com.wifochina.modules.event.entity.EventMessageEntity;
import java.util.Collections;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/25 17:32
 * @version 1.0
 */
@Slf4j
@Service
public class MqServiceImpl implements MqService {

	@Value("${alert.api.app-name}")
	private String appName;
	@Value("${alert.api.monitor}")
	private String monitor;

	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private AlertApiClient alertApiClient;


	@Override
	public void consume(DelayQueueMessage message) {
		// 事件缓存键
		String eventRedisKey = message.getEventRedisKey();
		// 事件信息
		EventMessageEntity eventMessage = message.getEventMessage();
		// 警告轮次
		Integer turns = message.getTurns();
		// 设定告警阈值时间
		long setTime = message.getSetTime();
		// 最多告警3次，之后删除缓存
		if (turns > 3) {
			stringRedisTemplate.delete(eventRedisKey);
			return;
		}
		// 事件恢复，直接删除缓存
		if (CommonConstants.EVENT_OFF.equals(eventMessage.getEventOnOff())) {
			stringRedisTemplate.delete(eventRedisKey);
			return;
		}
		// 事件仍在发生，则继续告警并推送消息
		if (stringRedisTemplate.hasKey(eventRedisKey)) {
			// 发送告警
			if (eventRedisKey.contains("stop")) {
				AlertCreateDTO alertCreateDTO = buildStopAlert(eventMessage);
				alertApiClient.sendAlert(alertCreateDTO);
			} else if (eventRedisKey.contains("offline")) {
				AlertCreateDTO alertCreateDTO = buildOfflineAlert(eventMessage);
				alertApiClient.sendAlert(alertCreateDTO);
			}
			// 推送下一条消息到队列
			message.setDelayTime(System.currentTimeMillis() + setTime * 60 * 1000);
      		message.setTurns(message.getTurns() + 1);
			DelayQueueInit.DELAY_QUEUE.offer(message);
		}
	}

	private AlertCreateDTO buildStopAlert(EventMessageEntity eventMessage) {
		return getAlertCreate(eventMessage, AlarmContentEnum.DEVICE_STOP);
	}

	private AlertCreateDTO buildOfflineAlert(EventMessageEntity eventMessage) {
		return getAlertCreate(eventMessage, AlarmContentEnum.DEVICE_OFFLINE);
	}

	@NotNull
	private AlertCreateDTO getAlertCreate(EventMessageEntity eventMessage, AlarmContentEnum alarmContentEnum) {
		AlertCreateDTO param = new AlertCreateDTO();
		param.setSource(appName);
		param.setSubGroup(eventMessage.getProjectId());
		BaseAlert baseAlert = getBaseAlert(eventMessage, alarmContentEnum);
		param.setAlerts(Collections.singletonList(baseAlert));
		return param;
	}

	@NotNull
	private BaseAlert getBaseAlert(EventMessageEntity eventMessage, AlarmContentEnum alarmContentEnum) {
		BaseAlert baseAlert = new BaseAlert();
		String alertName = alarmContentEnum.getName();
		String level = AlarmContentEnum.DEVICE_STOP.equals(alarmContentEnum) ?
				AlarmLevelEnum.critical.name() : AlarmLevelEnum.warning.name();
		baseAlert.setStatus(CommonConstants.EVENT_ON.equals(eventMessage.getEventOnOff()) ?
				BaseAlert.STATUS_FIRING : BaseAlert.STATUS_RESOLVE);
		baseAlert.setLabels(Map.of(
				CommonConstants.LABLE_ALERT_NAME, alertName,
				CommonConstants.LABLE_MONITOR, monitor,
				CommonConstants.LABLE_LEVEL, level));
		baseAlert.setAnnotations(Map.of(CommonConstants.ANNOTATION_DESCRIPTION, eventMessage.getEventDescription()));
		return baseAlert;
	}
}
