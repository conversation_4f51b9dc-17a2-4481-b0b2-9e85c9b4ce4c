package com.wifochina.common.handler;

import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;

/**
 * @Author: jacob
 * Date: 2022-03-03 15:40:59
 *
 * 致敬大师，彼岸无岸，当下即是
 */
@Component
public class MessageSourceHandler {


    private static MessageSource messageSource;

    public MessageSourceHandler(MessageSource messageSource) {
        MessageSourceHandler.messageSource = messageSource;
    }

    public static String getMessage(String messageCode) {
        return getMessage(messageCode, null);
    }

    public static String getMessage(String messageCode, Object[] arr) {

        HttpServletRequest request =((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        Locale locale = null;
        if (null != request) {
            locale = RequestContextUtils.getLocale(request);
        }
        if (null == locale) {
            locale = Locale.ENGLISH;
        }
        String message = messageSource.getMessage(messageCode, arr, locale);
        return message;
    }
}

