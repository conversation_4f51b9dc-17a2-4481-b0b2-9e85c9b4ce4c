package com.wifochina.modules.event.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import java.util.List;

/**
 * 告警开关服务类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface AlarmSwitchService extends IService<AlarmSwitchEntity> {

    List<AlarmSwitchEntity> getAlarmSwitchList(String projectId);

    AlarmCacheDTO listAlarmSwitch(String projectId);
}
