package com.wifochina.modules.event.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 时间信息告警信息表
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Getter
@Setter
@TableName("t_meter_event_code")
@ApiModel(value = "MeterEventCodeEntity对象", description = "时间信息告警信息表")
public class MeterEventCodeEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("代表类型CEM9000和BHE336")
    private String meterType;

    @ApiModelProperty(value = "事件等级Fault、State、Alarm")
    private String eventLevel;

    @ApiModelProperty("BIT码偏移量")
    private Integer bitOffset;

    @ApiModelProperty("BIT码")
    private Integer bitValue;

    @ApiModelProperty("事件描述")
    private String description;

    @ApiModelProperty("英文事件描述")
    private String descriptionEn;

    @ApiModelProperty("BIT位是0或者1有效")
    @TableField("digital_0_analog_1_control_2")
    private Integer digital0Analog1Control2;

    @ApiModelProperty(value = "是否触发，true为告警，false为正常")
    @TableField(exist = false)
    private Boolean value;

    @ApiModelProperty(value = "事件编码")
    private String eventCode;
}
