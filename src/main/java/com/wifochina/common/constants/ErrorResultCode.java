package com.wifochina.common.constants;

/**
 * @description: ErrorResultCode
 * @date: 2022/3/14 15:59
 * @author: jacob.sun
 * @version: 1.0
 */
public enum ErrorResultCode {

    /**
     * 系统内部错误
     */
    INTERNAL_SERVER_ERROR("INTERNAL_SERVER_ERROR"),
    /**
     * ID 不能为空
     */
    ILLEGAL_ID_NULL("ILLEGAL_ID_NULL"),
    /**
     * 系统内部错误-分发失败
     */
    DISTRIBUTION_FAILED("DISTRIBUTION_FAILED"),
    /**
     * 参数错误
     */
    ILLEGAL_ARGUMENT("ILLEGAL_ARGUMENT"),
    /**
     * 业务错误
     */
    SERVICE_EXCEPTION("SERVICE_EXCEPTION"),
    /**
     * 时间已重叠
     */
    TIME_OVERLAPPED("TIME_OVERLAPPED"),
    /**
     * 日期已重叠
     */
    DATE_OVERLAPPED("DATE_OVERLAPPED"),
    /**
     * 时间已重叠
     */
    DATETIME_IS_NULL("DATETIME_IS_NULL"),
    /**
     *  业务错误
     */
    REPEAT_IMPORT("REPEAT_IMPORT"),
    /**
     *  时间未排满
     */
    TIME_IS_NOT_FULL("TIME_IS_NOT_FULL"),
    /**
     *  日期未排满
     */
    DATE_IS_NOT_FULL("DATE_IS_NOT_FULL"),
    /**
     * 开始时间大于结束时间
     */
    START_GE_END_TIME("START_GE_END_TIME"),
    /**
     * 非法的数据格式，参数没有经过校验
     */
    ILLEGAL_DATA("ILLEGAL_DATA"),

    /**
     *超过项目上传文件大小限制
     */
    MULTIPART_TOO_LARGE("MULTIPART_TOO_LARGE"),
    /**
     * 非法状态
     */
    ILLEGAL_STATE("ILLEGAL_STATE"),

    /**
     * 缺少参数
     */
    MISSING_ARGUMENT("MISSING_ARGUMENT"),

    /**
     * 非法访问
     */
    ILLEGAL_ACCESS("ILLEGAL_ACCESS"),

    /**
     * 登录失败
     */
    LOGIN_FAIL("LOGIN_FAIL"),
    /**
     * 用户名或者密码错误
     */
    USER_PASSWORD_INVALID("USER_PASSWORD_INVALID"),
    /**
     * 用户名无效
     */
    USER_INVALID("USER_INVALID"),
    /**
     * 角色名重复
     */
    ROLE_REPEAT("ROLE_REPEAT"),
    /**
     * 密码无效
     */
    PWD_INVALID("PWD_INVALID"),
    /**
     * 权限不足
     */
    UNAUTHORIZED("UNAUTHORIZED"),
    /**
     * 没有数据
     */
    NO_DATA("NO_DATA"),
    /**
     * 错误的请求
     */
    METHOD_NOT_ALLOWED("METHOD_NOT_ALLOWED"),
    /**
     * 参数错误
     */
    ILLEGAL_ARGUMENT_TYPE("ILLEGAL_ARGUMENT_TYPE"),
    /**
     * 与协调控制器连接错误
     */
    FAIL_CONNECT_GOCONTROL("FAIL_CONNECT_GOCONTROL"),
    /**
     * "not" in string
     */
    NO_INT_STRING("NO_INT_STRING"),
    /**
     *
     */
    TO_LIMIT("TO_LIMIT"),
    /**
     * 密码最小长度限制
     */
    PASSWORD_MIN_LENGTH_FAIL("PASSWORD_MIN_LENGTH_FAIL"),
    /**
     * 用户名已存在
     */
    USER_EXISTS("USER_EXISTS"),
    /**
     * 字段超过长度
     */
    FILED_MIN_LENGTH_FAIL("%s_MAX_LENGTH_FAIL"),
    /**
     *
     */
    DATA_REFERENCED("DATA_REFERENCED"),
    /**
     *
     */
    DATA_REPEAT("DATA_REPEAT"),
    /**
     * 密码最大长度限制
     */
    PASSWORD_MAX_LENGTH_FAIL("PASSWORD_MAX_LENGTH_FAIL"),
    /**
     * 密码为空
     */
    PASSWORD_IS_EMPTY("PASSWORD_IS_EMPTY"),
    /**
     *
     */
    INSUFFICIENT_PASSWORD_STRENGTH("INSUFFICIENT_PASSWORD_STRENGTH"),
    /**
     *
     */
    PASSWORD_STRENGTH_NOT_CONFIGURED("PASSWORD_STRENGTH_NOT_CONFIGURED"),
    /**
     *
     */
    PASSWORD_STRENGTH_SIMPLE("ANY_CHARACTER"),
    /**
     *
     */
    PASSWORD_STRENGTH_NORMAL("LETTERS_AND_NUMBERS"),
    /**
     *
     */
    PASSWORD_STRENGTH_COMPLEX("UPPER_LOWER_CASE_LETTERS_NUMBERS"),
    /**
     *
     */
    PASSWORD_STRENGTH_SAFE("UPPER_LOWER_LETTERS_NUMBERS_SPECIAL_CHARACTERS"),

    /**
     * 连接成功
     */
    CONNECT_SUCCESS("CONNECT_SUCCESS"),

    /**
     * SOC 越界
     */
    SOC_EXCEED("SOC_EXCEED"),

    /**
     * ID 不能为空
     */
    IP_ILLEGAL("IP_ILLEGAL"),

    /**
     * 连接失败
     */
    CONNECT_FAILED("CONNECT_FAILED");

    private final String value;

    ErrorResultCode(String value) {
        this.value = value;
    }

    /**
     * Return the value of this status code.
     */
    public String value() {
        return this.value;
    }
}
