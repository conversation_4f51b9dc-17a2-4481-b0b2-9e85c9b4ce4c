package com.wifochina.common.constants;

import lombok.Getter;

/**
 * @since 5/24/2022 6:07 PM
 * <AUTHOR>
 * @version 1.0
 */
@Getter
public enum TypeCodeColumnEnum {
    /** 空调 */
    AIR("air_conditioner_type_code"),

    /** 消防 */
    FIRE("firefighting_type_code"),

    /** 水冷 */
    WATER_COOLER("water_cooler_type_code"),

    /** 功率转换器 */
    PCS("pcs_type_code"),

    /** 能量管理系统 */
    BMS("bms_type_code"),

    /** 系统（ems) */
    SYSTEM("system_type_code"),

    DCDC("dcdc_type_code"),

    // 不同index的可能不一样，-1 代表不可以用这个解析
    STATES("states_{i}_type_code");

    private final String code;

    TypeCodeColumnEnum(String code) {
        this.code = code;
    }
}
