package com.wifochina.modules.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wifochina.modules.alarm.entity.AlarmConfigEntity;
import java.util.List;

/**
 * 告警配置 服务类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface AlarmConfigService extends IService<AlarmConfigEntity> {

    /**
     * 根据项目ID查询告警配置列表
     *
     * @param projectId 项目ID
     * @return 告警配置列表
     */
    List<AlarmConfigEntity> getByProjectId(String projectId);
}
