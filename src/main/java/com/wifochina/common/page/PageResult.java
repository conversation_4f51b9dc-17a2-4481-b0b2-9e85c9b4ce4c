package com.wifochina.common.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
/**
 * <AUTHOR>
 */
@ApiModel(value = "分页结果返回对象", description = "分页结果返回对象")
public class PageResult<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "数据集合", notes = "数据集合")
	private List<T> rows; 

	@ApiModelProperty(value = "记录总数", notes = "记录总数")
	private long results = 0; 

	public List<T> getRows() {
		return rows;
	}

	public void setRows(List<T> rows) {
		this.rows = rows;
	}

	public long getResults() {
		return results;
	}

	public void setResults(long results) {
		this.results = results;
	}
}
