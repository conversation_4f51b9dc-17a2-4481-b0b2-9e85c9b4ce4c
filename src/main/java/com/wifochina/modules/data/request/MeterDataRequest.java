package com.wifochina.modules.data.request;

import com.wifochina.modules.data.entity.MeterContentData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @description: EmsDataRequest
 * @date: 7/14/2022 2:39 PM
 * @author: jacob.sun
 * @version: 1.0
 */
@Data
public class MeterDataRequest {

    @ApiModelProperty(value = "项目id", required = true)
    private String projectId;

    @ApiModelProperty(value = "电表数据", required = true)
    private Map<String, MeterContentData> metric;

    @ApiModelProperty(value = "采集时间", required = true)
    private long time;
}
