spring:
  datasource:
    #url: jdbc:p6spy:${DATABASE_HOST:mysql://**************:3306}/${DATABASE_NAME:ems}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull
    url: jdbc:${DATABASE_HOST:mysql://**************:3306}/${DATABASE_NAME:cloud}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useLegacyDatetimeCode=false&serverTimezone=UTC
    # url: jdbc:p6spy:${DATABASE_HOST:mysql://**************:3306}/${DATABASE_NAME:cloud}?autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useLegacyDatetimeCode=false&serverTimezone=UTC
    username: ${DATABASE_USERNAME:root}
    password: ${DATABASE_PASSWORD:123@abcd}
    type: com.alibaba.druid.pool.DruidDataSource
    #driver-class-name : com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: ${REDIS_HOST:**************}
    port: ${REDIS_PORT:6379}
    #password: ${REDIS_PASSWORD:wq2018!}
    database: ${REDIS_DATABASE:8}
    timeout: 10000
    jedis:
      pool:
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 1
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1

ems:
  version: v2.1.0
  task:
    enable: ${EMS_TASK:true}
  collect:
    project:
      id: ${PROJECT_ID:4f537620d37d40e19dd25be5ca6ad941}
  swagger2:
    enable: ${SWAGGER_ENABLE:true}
    basePackage: com.wifochina
    basePath:
    title: 储能EMS
    description: 储能EMS接口文档
    termsOfServiceUrl: http://localhost:8808/api/swagger-ui.html
    version: 2.0o
    apiName: Authorization
    apiKeyName: Authorization
swagger:
  enable: ${SWAGGER_ENABLE:true}

alert:
  api:
    base-url: ${ALERT_API_BASE_URL:http://wh-alert-manager.dev.weiheng-tech.com}
    app-name: Pangu
    monitor: ${ALERT_API_MONITOR:sh_dev}

