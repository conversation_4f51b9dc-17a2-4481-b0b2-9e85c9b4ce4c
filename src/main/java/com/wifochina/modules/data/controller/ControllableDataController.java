package com.wifochina.modules.data.controller;

import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.modules.alarm.service.AlarmHandler;
import com.wifochina.modules.data.listener.ControllableStateChange;
import com.wifochina.modules.data.request.ControllableChangeRequest;
import com.wifochina.modules.event.entity.EventCodeEntity;
import com.wifochina.modules.event.entity.EventMessageEntity;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.service.EventCodeService;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.group.entity.ControllableEntity;
import com.wifochina.modules.group.service.ControllableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @since 2024-03-21 11:32 AM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "data收集")
@RequestMapping("/data")
@RequiredArgsConstructor
public class ControllableDataController {

    private final EventCodeService eventCodeService;

    private final EventMessageService eventMessageService;

    private final HideEventCodeService hideEventCodeService;

    private final ControllableService controllableService;

    private final AlarmHandler alarmHandler;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    
    private Map<Integer, List<EventCodeEntity>> controllableEventCodeMap;

    public static final String CONTROLLABLE_STATE_BIT_PATTERN = "controllable_{i}_state_bit";
    public static final String CONTROLLABLE_ALARM_BIT_PATTERN = "controllable_{i}_alarm_bit";
    public static final String CONTROLLABLE_FAULT_BIT_PATTERN = "controllable_{i}_fault_bit";

    @PostConstruct
    public void init() {
        List<EventCodeEntity> eventCodeEntityList =
                eventCodeService
                        .lambdaQuery()
                        .like(EventCodeEntity::getPointColumn, "controllable")
                        .list();
        this.controllableEventCodeMap =
                eventCodeEntityList.stream()
                        .collect(Collectors.groupingBy(EventCodeEntity::getDeviceTypeCode));
    }

    @PostMapping("/controllableChange")
    @ApiOperation("可控事件")
    public Result<T> collectDeviceDataPoint(
            @RequestBody ControllableChangeRequest controllableChangeRequest) {
        controllableChangeRequest.setTime(
                String.valueOf(controllableChangeRequest.getTime()).length() == 13
                        ? controllableChangeRequest.getTime()
                        : controllableChangeRequest.getTime() * 1000);
        log.info("{} -->正在执行", Thread.currentThread().getName() + " 可控事件");
        publishControllableEvent(controllableChangeRequest);
        return Result.success();
    }

    private void publishControllableEvent(ControllableChangeRequest controllableChangeRequest) {
        String controllableId = controllableChangeRequest.getDeviceId();
        ControllableEntity controllableEntity = controllableService.getById(controllableId);
        if (controllableEntity == null) {
            return;
        }
        ControllableStateChange controllableStateChangeTemp =
                getControllableStateChange(controllableChangeRequest, controllableEntity);
        Optional.ofNullable(controllableChangeRequest.getCurrentStateCode())
                .ifPresent(
                        stateCode -> {
                            Integer[] lastStateCode = controllableChangeRequest.getLastStateCode();
                            handleControllableEvent(
                                    stateCode,
                                    lastStateCode,
                                    controllableStateChangeTemp,
                                    EventLevelEnum.STATE);
                        });
        Optional.ofNullable(controllableChangeRequest.getCurrentAlarmCode())
                .ifPresent(
                        stateCode -> {
                            Integer[] lastStateCode = controllableChangeRequest.getLastAlarmCode();
                            handleControllableEvent(
                                    stateCode,
                                    lastStateCode,
                                    controllableStateChangeTemp,
                                    EventLevelEnum.ALARM);
                        });
        Optional.ofNullable(controllableChangeRequest.getCurrentFaultCode())
                .ifPresent(
                        stateCode -> {
                            Integer[] lastStateCode = controllableChangeRequest.getLastFaultCode();
                            handleControllableEvent(
                                    stateCode,
                                    lastStateCode,
                                    controllableStateChangeTemp,
                                    EventLevelEnum.FAULT);
                        });
    }

    private static ControllableStateChange getControllableStateChange(
            ControllableChangeRequest controllableChangeRequest,
            ControllableEntity controllableEntity) {
        ControllableStateChange controllableStateChangeTemp = new ControllableStateChange();
        controllableStateChangeTemp.setControllableId(controllableEntity.getId());
        controllableStateChangeTemp.setControllableName(controllableEntity.getName());
        controllableStateChangeTemp.setVendor(controllableEntity.getVendor());
        controllableStateChangeTemp.setMaintain(controllableEntity.getMaintain());
        controllableStateChangeTemp.setProjectId(controllableEntity.getProjectId());
        controllableStateChangeTemp.setTypeCode(
                Integer.valueOf(controllableChangeRequest.getDeviceTypeCode()));
        controllableStateChangeTemp.setTime(controllableChangeRequest.getTime());
        return controllableStateChangeTemp;
    }

    public void handleControllableEvent(
            Integer[] stateCode,
            Integer[] lastStateCode,
            ControllableStateChange controllableStateChangeTemp,
            EventLevelEnum eventLevelEnum) {
        List<EventCodeEntity> eventCodeEntityList =
                controllableEventCodeMap.get(controllableStateChangeTemp.getTypeCode());
        Map<Integer, Map<Integer, EventCodeEntity>> stateEventCodeMap =
                getIntegerEventCodeMap(eventLevelEnum, eventCodeEntityList);
        if (stateEventCodeMap == null || stateCode == null || lastStateCode == null) {
            return;
        }
        processEvent(stateCode, lastStateCode, controllableStateChangeTemp, stateEventCodeMap);
    }

    private void processEvent(
            Integer[] stateCode,
            Integer[] lastStateCode,
            ControllableStateChange controllableStateChangeTemp,
            Map<Integer, Map<Integer, EventCodeEntity>> stateEventCodeMap) {
        List<HideEventCodeEntity> hideEventCodeEntityList =
                hideEventCodeService
                        .lambdaQuery()
                        .eq(
                                HideEventCodeEntity::getProjectId,
                                controllableStateChangeTemp.getProjectId())
                        // controllable 相关的hide 来自 t_event_code 也就是属于 ems 但是非 ems kernel 查询2即可
                        .eq(HideEventCodeEntity::getType, "2")
                        .list();
        List<String> hideEventCodeList =
                hideEventCodeEntityList.stream()
                        .map(HideEventCodeEntity::getEventCode)
                        .collect(Collectors.toList());
        for (int j = 0; j < stateCode.length; j++) {
            Integer state = stateCode[j];
            Integer lastState = lastStateCode[j];
            if (state == null || lastState == null) {
                continue;
            }
            // i=15 代表16位数据最大位
            for (int i = EmsConstants.CHAR_SIZE - 1; i >= 0; i--) {
                // 事件对象
                EventMessageEntity eventMessageEntity = new EventMessageEntity();
                // 设备id
                eventMessageEntity.setDeviceId(controllableStateChangeTemp.getControllableId());
                // 装备名称
                eventMessageEntity.setEquipName("CONTROLLABLE");
                // 是否维护
                eventMessageEntity.setMaintain(controllableStateChangeTemp.getMaintain());
                eventMessageEntity.setCreateTime(controllableStateChangeTemp.getTime());
                eventMessageEntity.setUpdateTime(controllableStateChangeTemp.getTime());
                if ((state & (1 << i)) != (lastState & (1 << i))) {
                    EventCodeEntity eventCodeEntity = stateEventCodeMap.get(j).get(i);
                    int finalI = i;
                    Optional.ofNullable(eventCodeEntity)
                            .ifPresent(
                                    eventCode ->
                                            // 事件描述 Fault、State、Alarm
                                            // (stateChange.getValue()&(1<<i))==0?0:1)
                                            // 如果位运算结果不等于0，证明改位为1
                                            saveEventMessage(
                                                    hideEventCodeList,
                                                    controllableStateChangeTemp,
                                                    state,
                                                    finalI,
                                                    eventMessageEntity,
                                                    eventCodeEntity));
                }
            }
        }
    }

    private void saveEventMessage(
            List<String> hideEventCodeList,
            ControllableStateChange controllableStateChangeTemp,
            Integer state,
            int finalI,
            EventMessageEntity eventMessageEntity,
            EventCodeEntity eventCodeEntity) {
        if ((state & (1 << finalI)) > 0) {
            eventMessageEntity.setEventOnOff("on");
            if (EventLevelEnum.FAULT.getLevel().equals(eventCodeEntity.getEventLevel())
                    || EventLevelEnum.ALARM.getLevel().equals(eventCodeEntity.getEventLevel())) {
                // 如果发生了警告和错误，让用户确认
                eventMessageEntity.setStatus(0);
            } else {
                eventMessageEntity.setStatus(1);
            }
        } else {
            eventMessageEntity.setEventOnOff("off");
            eventMessageEntity.setStatus(1);
        }
        eventMessageEntity.setEventDescription(eventCodeEntity.getEventDescription().trim());
        Optional.ofNullable(eventCodeEntity.getEventDescriptionEn())
                .ifPresent(
                        descriptionEn ->
                                eventMessageEntity.setEventDescriptionEn(descriptionEn.trim()));
        // 事件编码=typeCode_eventType_偏移量+bit码
        eventMessageEntity.setEventCode(
                eventCodeEntity.getBitOffset() + eventCodeEntity.getBitValue());
        // 装备类型：派能高压电池箱、汇川630kW PCS
        eventMessageEntity.setEquipType(controllableStateChangeTemp.getVendor());
        // 事件类型State、Fault、Alarm
        eventMessageEntity.setEventType(eventCodeEntity.getEventLevel());
        eventMessageEntity.setProjectId(controllableStateChangeTemp.getProjectId());
        eventMessageEntity.setEventKey(eventCodeEntity.getEventCode());
        eventMessageEntity.setWhetherHide(
                hideEventCodeList.contains(eventCodeEntity.getEventCode()));
        // 保存事件数据
        eventMessageService.save(eventMessageEntity);
        log.info("测控数据 {}", controllableStateChangeTemp);
        // 异步触发告警
        if (EventLevelEnum.FAULT.getLevel().equals(eventCodeEntity.getEventLevel())
                || EventLevelEnum.ALARM.getLevel().equals(eventCodeEntity.getEventLevel())) {
            threadPoolTaskExecutor.execute(() -> alarmHandler.handleAlarm(false,
                    eventCodeEntity.getDeviceType(), eventMessageEntity));
        }
    }

    private static Map<Integer, Map<Integer, EventCodeEntity>> getIntegerEventCodeMap(
            EventLevelEnum eventLevelEnum, List<EventCodeEntity> eventCodeEntityList) {
        Map<Integer, Map<Integer, EventCodeEntity>> stateEventCodeMap = null;
        switch (eventLevelEnum.getLevel()) {
            case "State":
                stateEventCodeMap =
                        getEventMap(eventCodeEntityList, CONTROLLABLE_STATE_BIT_PATTERN);
                break;
            case "Fault":
                stateEventCodeMap =
                        getEventMap(eventCodeEntityList, CONTROLLABLE_FAULT_BIT_PATTERN);
                break;
            case "Alarm":
                stateEventCodeMap =
                        getEventMap(eventCodeEntityList, CONTROLLABLE_ALARM_BIT_PATTERN);
                break;
            default:
        }
        return stateEventCodeMap;
    }

    private static Map<Integer, Map<Integer, EventCodeEntity>> getEventMap(
            List<EventCodeEntity> eventCodeEntityList, String s) {
        Map<Integer, Map<Integer, EventCodeEntity>> offsetValueMap = new HashMap<>();
        eventCodeEntityList.stream()
                .filter(e -> e.getPointColumn().contains(s))
                .forEach(
                        entity -> {
                            int key1 = entity.getBitOffset() / 16;
                            int key2 = entity.getBitValue();
                            if (!offsetValueMap.containsKey(key1)) {
                                offsetValueMap.put(key1, new HashMap<>());
                            }
                            offsetValueMap.get(key1).put(key2, entity);
                        });
        return offsetValueMap;
    }
}
