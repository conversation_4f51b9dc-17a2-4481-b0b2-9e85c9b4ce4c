package com.wifochina.modules.data.service;

import com.wifochina.modules.data.entity.PointDataEntity;
import com.wifochina.modules.data.listener.MeterStateChange;
import com.wifochina.modules.data.listener.StateChange;
import com.wifochina.modules.group.entity.DeviceEntity;

import java.util.List;
import java.util.Map;

public interface EventService {

    List<PointDataEntity> getPointDataEventList();

    Map<String, Integer> getEquipCountIndexMap();

    StateChange getStateChange(
            PointDataEntity pointDataEntity,
            int[] lastData,
            int index,
            int[] data,
            DeviceEntity deviceEntity,
            String deviceId,
            long time,
            int i);

    void handleEvent(List<String> hideEventCodes, StateChange stateChange);

    void handleMeterEvent(List<String> hideEventCodes, MeterStateChange meterStateChange);
}
