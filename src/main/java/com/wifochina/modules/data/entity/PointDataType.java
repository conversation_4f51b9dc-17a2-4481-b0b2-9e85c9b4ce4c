package com.wifochina.modules.data.entity;

/**
 * @Author: jacob
 * Date: 2022-03-03 00:16:51
 * <p>
 * 致敬大师，彼岸无岸，当下即是
 */
public enum PointDataType {

    /**
     * 16位无符号数据 int 接收
     */
    UINT16(1,"Uint16"),

    /**
     * 位数据
     */
    BIT(2,"BIT"),

    /**
     * ASCII数据
     */
    ASCII(3,"ASCII"),

    /**
     * 16位 short接收
     */
    INT16(4,"Int16"),


    /**
     * 16位 short接收
     */
    UINT32(5,"Uint32"),

    /**
     * 16位 short接收
     */
    INT32(6,"Int32");


    public static PointDataType getByCode(Integer code) {
        PointDataType[] values = values();
        for (PointDataType type : values) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return null;
    }

    public static PointDataType getByDesc(String desc) {
        PointDataType[] values = values();
        for (PointDataType type : values) {
            if (desc.equals(type.getDesc())) {
                return type;
            }
        }
        return null;
    }

    private Integer code;
    private String desc;

    PointDataType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
