package com.wifochina.modules.event.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wifochina.modules.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 告警信息表
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_event_code")
@ApiModel(value = "EventCodeEntity对象", description = "告警信息表")
public class EventCodeEntity extends BaseEntity implements Cloneable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "对应的点位字段")
    private String pointColumn;

    @ApiModelProperty(value = "设备类型码")
    private Integer deviceTypeCode;

    @ApiModelProperty(value = "设备类型名称")
    private String deviceType;

    @ApiModelProperty(value = "事件等级Fault、State、Alarm")
    private String eventLevel;

    @ApiModelProperty(value = "BIT码偏移量")
    private Integer bitOffset;

    @ApiModelProperty(value = "BIT码")
    private Integer bitValue;

    @ApiModelProperty(value = "事件描述")
    private String eventDescription;

    @ApiModelProperty(value = "英文事件描述")
    private String eventDescriptionEn;

    @ApiModelProperty(value = "BIT位是0或者1有效")
    private Integer bitStand;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "是否触发，true为告警，false为正常")
    @TableField(exist = false)
    private Boolean value;

    @ApiModelProperty(value = "事件编码")
    private String eventCode;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
