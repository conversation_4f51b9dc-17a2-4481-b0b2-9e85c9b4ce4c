package com.wifochina.modules.event.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.alarm.dtos.AlarmCacheDTO;
import com.wifochina.modules.event.entity.AlarmSwitchEntity;
import com.wifochina.modules.event.entity.ProjectExtEntity;
import com.wifochina.modules.event.mapper.AlarmSwitchMapper;
import com.wifochina.modules.event.service.AlarmSwitchService;
import com.wifochina.modules.event.service.ProjectExtService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 告警开关服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class AlarmSwitchServiceImpl extends ServiceImpl<AlarmSwitchMapper, AlarmSwitchEntity>
        implements AlarmSwitchService {

    @Resource private ProjectExtService projectExtService;

    @Override
    public List<AlarmSwitchEntity> getAlarmSwitchList(String projectId) {
        return list(
                Wrappers.<AlarmSwitchEntity>lambdaQuery()
                        .eq(AlarmSwitchEntity::getProjectId, projectId));
    }

    @Override
    public AlarmCacheDTO listAlarmSwitch(String projectId) {
        List<AlarmSwitchEntity> alarmSwitchList = getAlarmSwitchList(projectId);
        ProjectExtEntity projectExtEntity = projectExtService.getById(projectId);
        return AlarmCacheDTO.builder()
                .allEmsKernelAlarmEnabled(projectExtEntity.getAllEmsKernelAlarmEnabled())
                .allEmsSubDeviceAlarmEnabled(projectExtEntity.getAllEmsSubDeviceAlarmEnabled())
                .allMeterAlarmEnabled(projectExtEntity.getAllMeterAlarmEnabled())
                .alarmSwitchList(alarmSwitchList)
                .build();
    }
}
