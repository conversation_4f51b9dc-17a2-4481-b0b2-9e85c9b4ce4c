package com.wifochina.common.delay;

import com.wifochina.modules.event.entity.EventMessageEntity;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/25 18:56
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DelayQueueMessage implements Delayed {

	/**
	 * 延迟时间
	 */
	private long delayTime;

	/**
	 * 事件缓存key
	 */
	private String eventRedisKey;

	/**
	 * 推送事件信息
	 */
	private EventMessageEntity eventMessage;

	/**
	 * 设定时间(min)
	 */
	private long setTime;

	/**
	 * 轮次
	 */
	private Integer turns;

	public DelayQueueMessage(long delayTime) {
		this.delayTime = delayTime;
	}

	@Override
	public long getDelay(TimeUnit unit) {
		return unit.convert(this.delayTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
	}

	@Override
	public int compareTo(Delayed o) {
		long thisDelay = this.getDelay(TimeUnit.MILLISECONDS);
		long otherDelay = o.getDelay(TimeUnit.MILLISECONDS);
		return Long.compare(thisDelay, otherDelay);
	}
}
