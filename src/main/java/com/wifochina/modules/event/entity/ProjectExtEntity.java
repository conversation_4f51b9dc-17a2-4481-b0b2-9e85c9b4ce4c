package com.wifochina.modules.event.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022-03-19
 */
@Data
@EqualsAndHashCode()
@TableName("t_project_ext")
public class ProjectExtEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /** 项目区域id */
    private String areaId;

    /** 项目地点 */
    private String area;

    /** 项目规模 */
    private String size;

    /** 项目详细地址 */
    private String address;

    /** 用电类型 */
    private String powerType;

    /** 电压等级 */
    private String powerLevel;

    /** 项目经度 */
    private Double longitude;

    /** 项目维度 */
    private Double latitude;

    /** 项目备注 */
    private String remark;

    /** ems数量 */
    private Integer ems;

    /** pcs数量 */
    private Integer pcs;

    /** bms数量 */
    private Integer bms;

    @ApiModelProperty(value = "是否采集光伏数据:false 不采集,true采集")
    private Boolean enableRadiation;

    @ApiModelProperty(value = "是否采集天气数据:false 不采集,true采集")
    private Boolean enableWeather;

    @ApiModelProperty(value = "是否预测:false 不采集,true采集")
    private Boolean enablePrediction;

    @ApiModelProperty(value = "预测是否采集数据:false 不采集,true采集")
    private Boolean enableEtl;

    @ApiModelProperty(value = "项目标识和预测路径")
    private String predictionPath;

    @ApiModelProperty(value = "隐藏所有 ems 核心code")
    private Boolean hideAllEmsKernelCode;

    @ApiModelProperty(value = "隐藏所有 ems 子设备 code")
    private Boolean hideAllEmsSubDeviceCode;

    @ApiModelProperty(value = "隐藏 meter code")
    private Boolean hideAllMeterCode;

    @ApiModelProperty(value = "是否启用所有 ems 核心告警")
    private Boolean allEmsKernelAlarmEnabled;

    @ApiModelProperty(value = "是否启用ems 子设备告警")
    private Boolean allEmsSubDeviceAlarmEnabled;

    @ApiModelProperty(value = "是否启用所有 meter 告警")
    private Boolean allMeterAlarmEnabled;
}
