package com.wifochina.common.constants;

/**
 * <AUTHOR>
 */

public enum UserErrorResultCode {

    /**
     * 用户名不存在
     */
    USER_NOT_EXISTS("USER_NOT_EXISTS"),

    /**
     * 邮箱已经注册
     */
    EMAIL_EXIST_ERROR("EMAIL_EXIST_ERROR"),

    /**
     * 邮箱重复
     */
    EMAIL_REPEAT_ERROR("EMAIL_REPEAT_ERROR"),

    /**
     * 邮箱格式不合法
     */
    ILLEGAL_EMAIL_FORMAT("ILLEGAL_EMAIL_FORMAT"),

    /**
     * 手机号重复
     */
    MOBILE_REPEAT_ERROR("MOBILE_REPEAT_ERROR"),

    /**
     * 手机号已经注册
     */
    MOBILE_EXIST_ERROR("MOBILE_EXIST_ERROR"),


    /**
     * 数值超出指定范围 0 - 100
     */
    VALUE_OUT_RANGE("VALUE_OUT_RANGE"),

    /**
     * 密码不一致
     */
    PASSWORD_ARE_INCONSISTENT("PASSWORD_ARE_INCONSISTENT"),


    /**
     * 接受邮箱提醒需填写完整信息
     */
    INCOMPLETE_NOT_INFO("INCOMPLETE_NOT_INFO"),

    /**
     * 数值范围在1-100之间
     */
    VALUE_NOT_RANGE_ONE_TO_HUNDRED("VALUE_NOT_RANGE_ONE_TO_HUNDRED"),

    /**
     * 数值范围在80-100之间
     */
    VALUE_NOT_RANGE_EIGHTY_TO_HUNDRED("VALUE_NOT_RANGE_EIGHTY_TO_HUNDRED"),

    /**
     * 用户名已存在
     */
    USER_NAME_ALREADY_EXISTS("USER_NAME_ALREADY_EXISTS"),

    /**
     * 用户密码错误
     */
    USER_PASSWORD_MISTYPED("USER_PASSWORD_MISTYPED"),

    /**
     * 请填写完整信息
     */
    REGISTRATION_PARAMETER_IS_NULL("REGISTRATION_PARAMETER_IS_NULL"),

    /**
     * 请输入与国家区号对应的手机号码
     */
    AREA_CODE_DOSE_NOT_MATCH("AREA_CODE_DOSE_NOT_MATCH"),


    INVERTER_BELONGS_ANOTHER_ACCOUNT("INVERTER_BELONGS_ANOTHER_ACCOUNT"),

    SN_KEY_VALIDATION_ERROR("SN_KEY_VALIDATION_ERROR"),

    ENDTIME_SHOULD_LESS_STARTTIME("ENDTIME_SHOULD_LESS_STARTTIME"),

    MULTIPLE_TIMES_CANNOT_INTERSECT("MULTIPLE_TIMES_CANNOT_INTERSECT"),

    WRONG_TIME_FORMAT("WRONG_TIME_FORMAT"),

    /**
     * 用户还未添加设备
     */
    USER_DEVICE_NOT_EXISTS("USER_DEVICE_NOT_EXISTS"),


    POWER_RESERVE_OUT_RANGE("POWER_RESERVE_OUT_RANGE"),

    MINIMUM_RESERVE_OUT_RANGE("MINIMUM_RESERVE_OUT_RANGE")
    ;


    private final String value;


    UserErrorResultCode(String value) {
        this.value = value;
    }


    /**
     * Return the integer value of this status code.
     */
    public String value() {
        return this.value;
    }


}
