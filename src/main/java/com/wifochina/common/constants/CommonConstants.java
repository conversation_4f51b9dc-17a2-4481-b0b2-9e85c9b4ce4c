package com.wifochina.common.constants;

/**
 * <AUTHOR>
 * @date 2025/8/5 16:22
 * @version 1.0
 */
public interface CommonConstants {

    String ALARM_SWITCH_REDIS_KEY = "alarm_switch:%s";

    static String buildAlarmSwitchRedisKey(String projectId) {
        return String.format(ALARM_SWITCH_REDIS_KEY, projectId);
    }

    String STOP_REDIS_KEY = "event:%s:stop:%s";

    static String buildStopRedisKey(String projectId, String eventCode) {
        return String.format(STOP_REDIS_KEY, projectId, eventCode);
    }

    String OFFLINE_REDIS_KEY = "event:%s:offline:%s";

    static String buildOfflineRedisKey(String projectId, String deviceId) {
        return String.format(OFFLINE_REDIS_KEY, projectId, deviceId);
    }

    String DEVICE_TYPE_EMS_KERNEL = "EMS Kernel";
    String LABLE_ALERT_NAME = "alertname";
    String LABLE_MONITOR = "monitor";
    String LABLE_LEVEL = "level";
    String ANNOTATION_DESCRIPTION = "description";

    // 停机告警event_code
    String EVENT_CODE_STOP1 = "65534_State_0";
    String EVENT_CODE_STOP2 = "65535_State_0";

    static boolean isStopEvent(String eventCode) {
        return EVENT_CODE_STOP1.equals(eventCode) || EVENT_CODE_STOP2.equals(eventCode);
    }

    String EVENT_ON = "on";
    String EVENT_OFF = "off";
}
