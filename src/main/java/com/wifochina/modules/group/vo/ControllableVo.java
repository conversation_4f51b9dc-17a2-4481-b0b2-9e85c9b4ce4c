package com.wifochina.modules.group.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 协调控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Getter
@Setter
@ApiModel(value = "Controllable Vo")
public class ControllableVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("uuid")
    private String id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("型号")
    private String vendor;

}
