package com.wifochina.modules.event.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.mapper.HideEventCodeMapper;
import com.wifochina.modules.event.service.HideEventCodeService;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-31 19:22:30
 */
@Service
public class HideEventCodeServiceImpl extends ServiceImpl<HideEventCodeMapper, HideEventCodeEntity>
        implements HideEventCodeService {

    @Override
    public List<String> getEmsHideEventCode(String projectId) {
        List<HideEventCodeEntity> list =
                this.lambdaQuery()
                        .eq(HideEventCodeEntity::getProjectId, projectId)
                        // 应该是都要用到 类型去掉
                        .in(HideEventCodeEntity::getType, List.of("1", "2"))
                        //                        .eq(HideEventCodeEntity::getType, "EMS")
                        .list();
        return list.stream().map(HideEventCodeEntity::getEventCode).collect(Collectors.toList());
    }

    @Override
    public List<String> getMeterHideEventCode(String projectId) {
        List<HideEventCodeEntity> list =
                this.lambdaQuery()
                        .eq(HideEventCodeEntity::getProjectId, projectId)
                        .eq(HideEventCodeEntity::getType, "3")
                        //                        .eq(HideEventCodeEntity::getType, "METER")
                        .list();
        return list.stream().map(HideEventCodeEntity::getEventCode).collect(Collectors.toList());
    }
}
