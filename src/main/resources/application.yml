server:
  port: 8809
  tomcat:
    uri-encoding: utf-8
  servlet:
    session:
      timeout: 100000000
    context-path: /api

spring:
  application:
    name: wifo-china.ems
  profiles:
    active: ${PROFILES_ACTIVE:cloud}
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
  mvc:
    async:
      request-timeout: 600000
    path match:
      matching-strategy: ant_path_matcher
  devtools:
    restart:
      enabled: true

mybatis-plus:
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:com/wifochina/modules/*/mapper/xml/*Mapper.xml
  global-config:
    db-config:
      #主键类型 AUTO:"数据库ID自增" INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断"  NOT_NULL:"非 NULL 判断")  NOT_EMPTY:"非空判断"
      field-strategy: IGNORED

  configuration:
    cache-enabled: true
    use-generated-keys: true
    default-executor-type: REUSE
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true

influx:
  #influxdb服务器的地址
  url: ${TSDB_URL:http://influx-proxy.dev.weiheng-tech.com}
  token: ${TSDB_TOKEN:8ydw+mbBxkpzsrxy}
  org: weiheng
  bucket:
    realtime: ${BUCKET_REALTIME:ems_cloud_dev}
    forever: ${BUCKET_FOREVER:ems_forever_dev}
    mean: ${BUCKET_MEAN:ems_mean_dev}
    demand: ${BUCKET_DEMAND:ems_demand_dev}