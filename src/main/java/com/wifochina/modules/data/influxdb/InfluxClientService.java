package com.wifochina.modules.data.influxdb;

import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.util.EmsUtil;
import java.util.ArrayList;
import java.util.List;

/**
 * @since 2024-05-06 12:10 PM
 * <AUTHOR>
 */
public interface InfluxClientService {

    String getBucketRealtime();

    String getBucketForever();

    String getBucketMean();

    String getBucketDemand();

    String getTableEms();

    String getTableMeter();

    String getTableDemand1mSliding();

    String getTableDemand15mFixed();

    String getTableDemand30mFixed();

    String getEmsTable(String projectId);

    String getMeterTable(String projectId);

    String getCellTable(String projectId);

    QueryApi getQueryApi();

    WriteApiBlocking getWriteApi();

    String getDeviceKey();

    String getMeterKey();

    default String createMeterSql(List<String> ammeterIds) {
        return EmsUtil.createMeterSqlForInfluxDb(ammeterIds);
    }

    default List<Restrictions> createMeterRestrictions(List<String> meterList) {
        List<Restrictions> deviceRestrictions = new ArrayList<>(meterList.size());
        for (String meter : meterList) {
            deviceRestrictions.add(Restrictions.tag("meterId").equal(meter));
        }
        return deviceRestrictions;
    }
}
